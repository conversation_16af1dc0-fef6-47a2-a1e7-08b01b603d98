import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';
import 'package:video_player/video_player.dart';

/// Enum to define the different states of the video widget
enum VideoWidgetState {
  /// Default state - shows video name with play icon
  defaultState,

  /// Hover state - highlighted border and play icon
  hover,

  /// Playing state - video is playing in container
  playing,

  /// Maximized state - video is playing in fullscreen overlay
  maximized,
}

/// A widget that provides video playback functionality with 4 distinct states.
///
/// This widget transitions through different states:
/// 1. Default: Simple input-like display with video name
/// 2. Hover: Highlighted border and play icon
/// 3. Playing: Video plays in contained area with controls
/// 4. Maximized: Video plays in fullscreen overlay
class VideoWidget extends StatefulWidget {
  /// URL of the video file to play
  final String? url;

  /// Asset path of the video file to play
  final String? assetPath;

  /// File path of the video file to play
  final String? filePath;

  /// Display name for the video
  final String videoName;

  /// File name to show when playing
  final String? fileName;

  /// Whether to autoplay when entering play state
  final bool autoplay;

  /// Whether to loop the video
  final bool loop;

  /// Initial volume (0.0 to 1.0)
  final double initialVolume;

  /// Theme color for controls and highlights
  final Color? themeColor;

  /// Background color
  final Color? backgroundColor;

  /// Text color
  final Color? textColor;

  /// Border color for default state
  final Color? borderColor;

  /// Border color for hover state
  final Color? hoverBorderColor;

  /// Icon color
  final Color? iconColor;

  /// Hover icon color
  final Color? hoverIconColor;

  /// Custom play icon
  final IconData playIcon;

  /// Custom pause icon
  final IconData pauseIcon;

  /// Custom fullscreen icon
  final IconData fullscreenIcon;

  /// Custom close icon
  final IconData closeIcon;

  /// Width of the widget
  final double? width;

  /// Height of the widget in default/hover state
  final double? height;

  /// Height of the widget in playing state
  final double? playingHeight;

  /// Border radius
  final double borderRadius;

  /// Border width
  final double borderWidth;

  /// Whether to show video controls in playing state
  final bool showControls;

  /// Whether to show fullscreen button
  final bool showFullscreenButton;

  /// Whether to show volume control
  final bool showVolumeButton;

  /// Whether to show progress bar
  final bool showProgressBar;

  /// Callback when video starts playing
  final VoidCallback? onPlay;

  /// Callback when video is paused
  final VoidCallback? onPause;

  /// Callback when video ends
  final VoidCallback? onComplete;

  /// Callback when entering fullscreen
  final VoidCallback? onEnterFullscreen;

  /// Callback when exiting fullscreen
  final VoidCallback? onExitFullscreen;

  /// Callback when an error occurs
  final Function(String)? onError;

  /// Callback for hover state changes
  final Function(bool)? onHover;

  /// Focus node for keyboard control
  final FocusNode? focusNode;

  /// Whether to autofocus
  final bool autofocus;

  /// Creates a video widget.
  const VideoWidget({
    super.key,
    this.url,
    this.assetPath,
    this.filePath,
    this.videoName = 'Video Name',
    this.fileName,
    this.autoplay = false,
    this.loop = false,
    this.initialVolume = 0.7,
    this.themeColor,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.hoverBorderColor,
    this.iconColor,
    this.hoverIconColor,
    this.playIcon = Icons.play_arrow,
    this.pauseIcon = Icons.pause,
    this.fullscreenIcon = Icons.fullscreen,
    this.closeIcon = Icons.close,
    this.width,
    this.height,
    this.playingHeight,
    this.borderRadius = 4.0,
    this.borderWidth = 1.0,
    this.showControls = true,
    this.showFullscreenButton = true,
    this.showVolumeButton = true,
    this.showProgressBar = true,
    this.onPlay,
    this.onPause,
    this.onComplete,
    this.onEnterFullscreen,
    this.onExitFullscreen,
    this.onError,
    this.onHover,
    this.focusNode,
    this.autofocus = false,
  });

  /// Creates a VideoWidget from a JSON map
  factory VideoWidget.fromJson(Map<String, dynamic> json) {
    // Helper function to parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        switch (colorValue.toLowerCase()) {
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'red':
            return Colors.red;
          case 'green':
            return Colors.green;
          case 'blue':
            return Colors.blue;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'transparent':
            return Colors.transparent;
          default:
            if (colorValue.startsWith('#')) {
              try {
                final hexColor = colorValue.substring(1);
                final hexValue = int.parse(
                  '0xFF${hexColor.padRight(8, 'F').substring(0, 8)}',
                );
                return Color(hexValue);
              } catch (e) {
                return null;
              }
            }
            return null;
        }
      } else if (colorValue is int) {
        return Color(colorValue);
      }

      return null;
    }

    // Helper function to parse icons
    IconData parseIcon(dynamic iconValue, IconData defaultIcon) {
      if (iconValue == null) return defaultIcon;

      if (iconValue is String) {
        switch (iconValue.toLowerCase()) {
          case 'play':
          case 'play_arrow':
            return Icons.play_arrow;
          case 'pause':
            return Icons.pause;
          case 'fullscreen':
            return Icons.fullscreen;
          case 'close':
            return Icons.close;
          case 'stop':
            return Icons.stop;
          case 'volume_up':
            return Icons.volume_up;
          case 'volume_off':
            return Icons.volume_off;
          default:
            return defaultIcon;
        }
      }

      return defaultIcon;
    }

    return VideoWidget(
      url: json['url'] as String?,
      assetPath: json['assetPath'] as String?,
      filePath: json['filePath'] as String?,
      videoName: json['videoName'] as String? ?? 'Video Name',
      fileName: json['fileName'] as String?,
      autoplay: json['autoplay'] as bool? ?? false,
      loop: json['loop'] as bool? ?? false,
      initialVolume: (json['initialVolume'] as num?)?.toDouble() ?? 0.7,
      themeColor: parseColor(json['themeColor']),
      backgroundColor: parseColor(json['backgroundColor']),
      textColor: parseColor(json['textColor']),
      borderColor: parseColor(json['borderColor']),
      hoverBorderColor: parseColor(json['hoverBorderColor']),
      iconColor: parseColor(json['iconColor']),
      hoverIconColor: parseColor(json['hoverIconColor']),
      playIcon: parseIcon(json['playIcon'], Icons.play_arrow),
      pauseIcon: parseIcon(json['pauseIcon'], Icons.pause),
      fullscreenIcon: parseIcon(json['fullscreenIcon'], Icons.fullscreen),
      closeIcon: parseIcon(json['closeIcon'], Icons.close),
      width: (json['width'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      playingHeight: (json['playingHeight'] as num?)?.toDouble(),
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      showControls: json['showControls'] as bool? ?? true,
      showFullscreenButton: json['showFullscreenButton'] as bool? ?? true,
      showVolumeButton: json['showVolumeButton'] as bool? ?? true,
      showProgressBar: json['showProgressBar'] as bool? ?? true,
      autofocus: json['autofocus'] as bool? ?? false,
      // Note: Callback functions cannot be serialized from JSON
      onPlay: json['onPlay'] == true ? () => debugPrint('Video play') : null,
      onPause: json['onPause'] == true ? () => debugPrint('Video pause') : null,
      onComplete:
          json['onComplete'] == true
              ? () => debugPrint('Video complete')
              : null,
      onEnterFullscreen:
          json['onEnterFullscreen'] == true
              ? () => debugPrint('Enter fullscreen')
              : null,
      onExitFullscreen:
          json['onExitFullscreen'] == true
              ? () => debugPrint('Exit fullscreen')
              : null,
      onError:
          json['onError'] == true
              ? (error) => debugPrint('Video error: $error')
              : null,
      onHover:
          json['onHover'] == true
              ? (isHovered) => debugPrint('Video hover: $isHovered')
              : null,
    );
  }

  @override
  State<VideoWidget> createState() => _VideoWidgetState();
}

class _VideoWidgetState extends State<VideoWidget>
    with SingleTickerProviderStateMixin {
  VideoWidgetState _currentState = VideoWidgetState.defaultState;
  VideoPlayerController? _videoPlayerController;
  bool _isPlaying = false;
  bool _isLoading = false;
  bool _isMuted = false;
  double _volume = 0.7;
  String? _errorMessage;
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  @override
  void initState() {
    super.initState();
    _volume = widget.initialVolume;
    _focusNode = widget.focusNode ?? FocusNode();

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _removeOverlay();
    _videoPlayerController?.dispose();
    _animationController.dispose();
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _initializeVideo() {
    if (_videoPlayerController != null) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      String? source;
      if (widget.url != null) {
        source = widget.url;
      } else if (widget.assetPath != null) {
        source = widget.assetPath;
      } else if (widget.filePath != null) {
        source = widget.filePath;
      }

      if (source == null) {
        setState(() {
          _errorMessage = 'No video source provided';
          _isLoading = false;
        });
        return;
      }

      // Create the appropriate VideoPlayerController
      if (source.startsWith('http://') || source.startsWith('https://')) {
        _videoPlayerController = VideoPlayerController.networkUrl(
          Uri.parse(source),
        );
      } else if (source.startsWith('asset:')) {
        _videoPlayerController = VideoPlayerController.asset(
          source.replaceFirst('asset:', ''),
        );
      } else {
        _videoPlayerController = VideoPlayerController.file(File(source));
      }

      _videoPlayerController!
          .initialize()
          .then((_) {
            setState(() {
              _isLoading = false;
            });

            _videoPlayerController!.setVolume(_isMuted ? 0 : _volume);
            _setupListeners();

            if (widget.autoplay) {
              _play();
            }
          })
          .catchError((error) {
            setState(() {
              _errorMessage = 'Failed to load video: $error';
              _isLoading = false;
            });

            if (widget.onError != null) {
              widget.onError!('Failed to load video: $error');
            }
          });
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading video: $e';
        _isLoading = false;
      });

      if (widget.onError != null) {
        widget.onError!('Error loading video: $e');
      }
    }
  }

  void _setupListeners() {
    if (_videoPlayerController == null) return;

    _videoPlayerController!.addListener(() {
      final isPlaying = _videoPlayerController!.value.isPlaying;

      if (isPlaying != _isPlaying) {
        setState(() {
          _isPlaying = isPlaying;
        });

        if (isPlaying && widget.onPlay != null) {
          widget.onPlay!();
        } else if (!isPlaying && widget.onPause != null) {
          widget.onPause!();
        }
      }

      // Check if video completed
      if (_videoPlayerController!.value.position >=
          _videoPlayerController!.value.duration) {
        if (widget.onComplete != null) {
          widget.onComplete!();
        }
      }
    });
  }

  void _play() {
    if (_videoPlayerController == null) return;
    _videoPlayerController!.play();
  }

  void _pause() {
    if (_videoPlayerController == null) return;
    _videoPlayerController!.pause();
  }

  void _togglePlayPause() {
    if (_isPlaying) {
      _pause();
    } else {
      _play();
    }
  }

  void _onHover(bool isHovered) {
    if (widget.onHover != null) {
      widget.onHover!(isHovered);
    }

    // Only handle hover states when in default or hover state (not when playing or maximized)
    if (_currentState == VideoWidgetState.defaultState ||
        _currentState == VideoWidgetState.hover) {
      setState(() {
        _currentState =
            isHovered ? VideoWidgetState.hover : VideoWidgetState.defaultState;
      });

      if (isHovered) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  void _onTap() {
    if (_currentState == VideoWidgetState.defaultState ||
        _currentState == VideoWidgetState.hover) {
      // Transition to playing state
      setState(() {
        _currentState = VideoWidgetState.playing;
      });
      _initializeVideo();
    }
  }

  void _onClose() {
    setState(() {
      _currentState = VideoWidgetState.defaultState;
    });
    _videoPlayerController?.pause();
  }

  void _onMaximize() {
    setState(() {
      _currentState = VideoWidgetState.maximized;
    });
    _showOverlay();

    if (widget.onEnterFullscreen != null) {
      widget.onEnterFullscreen!();
    }
  }

  void _onExitMaximize() {
    setState(() {
      _currentState = VideoWidgetState.playing;
    });
    _removeOverlay();

    if (widget.onExitFullscreen != null) {
      widget.onExitFullscreen!();
    }
  }

  void _showOverlay() {
    _overlayEntry = OverlayEntry(builder: (context) => _buildMaximizedView());
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  Widget _buildDefaultState() {
    final effectiveBorderColor =
        _currentState == VideoWidgetState.hover
            ? (widget.hoverBorderColor ??
                widget.themeColor ??
                const Color(0xFF0058FF))
            : (widget.borderColor ?? const Color(0xFFCCCCCC));

    final effectiveIconColor =
        _currentState == VideoWidgetState.hover
            ? (widget.hoverIconColor ??
                widget.themeColor ??
                const Color(0xFF0058FF))
            : (widget.iconColor ?? const Color(0xFFCCCCCC));

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: widget.width,
            height: widget.height ?? _getResponsiveHeight(context),
            padding: _getResponsivePadding(context),
            decoration: BoxDecoration(
              color: widget.backgroundColor ?? Colors.white,
              borderRadius: BorderRadius.circular(widget.borderRadius),
              border: Border.all(
                color: effectiveBorderColor,
                width: widget.borderWidth,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.videoName,
                    // style: TextStyle(
                    //   color: (widget.textColor ?? Colors.black).withOpacity(
                    //     0.6,
                    //   ),
                    //   fontSize: _getResponsiveValueFontSize(context),
                    // ),
                    style: FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontWeight: FontManager.medium,
                      color: (widget.textColor ?? Colors.black).withOpacity(
                        0.6,
                      ),
                      fontSize: _getResponsiveValueFontSize(context),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Icon(
                //   widget.playIcon,
                //   color: effectiveIconColor,
                //   size: _getResponsiveIconSize(context),
                // ),
                SvgPicture.asset(
                  _currentState == VideoWidgetState.hover
                      ? 'assets/images/play-video.svg'
                      : 'assets/images/play.svg',
                  package: 'ui_controls_library',

                  //width: _getResponsiveIconSize(context),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildPlayingState() {
    if (_errorMessage != null) {
      return _buildErrorView();
    }

    if (_isLoading) {
      return _buildLoadingView();
    }

    if (_videoPlayerController == null ||
        !_videoPlayerController!.value.isInitialized) {
      return _buildLoadingView();
    }

    return Container(
      width: widget.width,
      height: widget.playingHeight ?? 148,
      decoration: BoxDecoration(
        color: Colors.black,
        //borderRadius: BorderRadius.circular(widget.borderRadius),
        borderRadius: BorderRadius.circular(0),
      ),
      child: Stack(
        children: [
          // Video player
          ClipRRect(
            //borderRadius: BorderRadius.circular(widget.borderRadius),
            borderRadius: BorderRadius.circular(0),
            child: AspectRatio(
              aspectRatio: _videoPlayerController!.value.aspectRatio,
              child: VideoPlayer(_videoPlayerController!),
            ),
          ),

          //Video info overlay
          Positioned(
            top: 1,
            left: 1,
            right: 1,
            child: Container(
              // padding: const EdgeInsets.symmetric(horizontal: 12, vertical:0),
              // decoration: BoxDecoration(
              //   color: Colors.white,
              //   borderRadius: BorderRadius.circular(4),
              // ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Expanded(
                  //   child: Column(
                  //     crossAxisAlignment: CrossAxisAlignment.start,
                  //     mainAxisSize: MainAxisSize.min,
                  //     children: [
                  //       Text(
                  //         widget.videoName,
                  //         style: const TextStyle(
                  //           color: Color(0xFF333333),
                  //           fontSize: 14,
                  //           fontWeight: FontWeight.bold,
                  //         ),
                  //       ),
                  //       if (widget.fileName != null)
                  //         Text(
                  //           widget.fileName!,
                  //           style: const TextStyle(
                  //             color: Colors.white70,
                  //             fontSize: 12,
                  //           ),
                  //         ),
                  //     ],
                  //   ),
                  // ),
                  IconButton(
                    icon: Icon(
                      widget.closeIcon,
                      color: Color(0xFF333333),
                      size: 20,
                    ),
                    onPressed: _onClose,
                  ),
                ],
              ),
            ),
          ),

          // Controls overlay
          if (widget.showControls)
            Positioned(bottom: 1, left: 1, right: 1, child: _buildControls()),
        ],
      ),
    );
  }

  Widget _buildMaximizedView() {
    return Material(
      color: Colors.black.withOpacity(0.9),
      child: Stack(
        children: [
          // Close background tap
          Positioned.fill(
            child: GestureDetector(
              onTap: _onExitMaximize,
              child: Container(color: Colors.transparent),
            ),
          ),

          // Video player centered
          Center(
            child: Container(
              width: MediaQuery.of(context).size.width * 0.9,
              height: MediaQuery.of(context).size.height * 0.8,
              child:
                  _videoPlayerController != null &&
                          _videoPlayerController!.value.isInitialized
                      ? AspectRatio(
                        aspectRatio: _videoPlayerController!.value.aspectRatio,
                        child: VideoPlayer(_videoPlayerController!),
                      )
                      : _buildLoadingView(),
            ),
          ),

          // Controls overlay
          if (widget.showControls)
            Positioned(
              bottom: 50,
              left: 50,
              right: 50,
              child: _buildControls(isMaximized: true),
            ),

          // Close button
          Positioned(
            top: 50,
            right: 50,
            child: IconButton(
              icon: Icon(widget.closeIcon, color: Colors.white, size: 30),
              onPressed: _onExitMaximize,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControls({bool isMaximized = false}) {
    if (_videoPlayerController == null ||
        !_videoPlayerController!.value.isInitialized) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.55),
        borderRadius: BorderRadius.circular(0),
      ),
      child: Row(
        children: [
          // Play/Pause button
          Container(
            decoration: BoxDecoration(
              color: Color(0xFF0058FF), // your background color
              borderRadius: BorderRadius.circular(
                50.0,
              ), // optional rounded corners
            ),
            child: IconButton(
              icon: Icon(
                _isPlaying ? widget.pauseIcon : widget.playIcon,
                color: Colors.white,
                size: isMaximized ? 24 : 20,
              ),
              onPressed: _togglePlayPause,
              padding: EdgeInsets.all(
                2,
              ), // <-- Reduced padding for selection icon
              constraints: BoxConstraints(),
            ),
          ),
          SizedBox(width: 4),
          // Progress bar
          if (widget.showProgressBar)
            Expanded(
              child: VideoProgressIndicator(
                _videoPlayerController!,
                allowScrubbing: true,
                colors: VideoProgressColors(
                  playedColor: widget.themeColor ?? const Color(0xFF0058FF),
                  bufferedColor: Colors.white.withOpacity(0.3),
                  backgroundColor: Colors.white.withOpacity(0.1),
                ),
                padding: EdgeInsets.all(0),
              ),
            ),

          // Duration
          const SizedBox(width: 8),
          Text(
            _formatDuration(_videoPlayerController!.value.position) +
                ' / ' +
                _formatDuration(_videoPlayerController!.value.duration),
            //style: const TextStyle(color: Colors.white, fontSize: 12),
            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontWeight: FontManager.medium,
              color: Colors.white,
              fontSize: _getResponsiveFontSize(context),
            ),
          ),

          // Volume button
          if (widget.showVolumeButton)
            IconButton(
              icon: Icon(
                _isMuted ? Icons.volume_off : Icons.volume_up,
                color: Colors.white,
                size: isMaximized ? 24 : 20,
              ),
              onPressed: () {
                setState(() {
                  _isMuted = !_isMuted;
                });
                _videoPlayerController!.setVolume(_isMuted ? 0 : _volume);
              },
            ),

          // Fullscreen button
          if (widget.showFullscreenButton && !isMaximized)
            IconButton(
              icon: Icon(widget.fullscreenIcon, color: Colors.white, size: 20),
              onPressed: _onMaximize,
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingView() {
    return Container(
      width: widget.width,
      height: widget.playingHeight ?? 300,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      child: Center(
        child: CircularProgressIndicator(
          color: widget.themeColor ?? const Color(0xFF0058FF),
        ),
      ),
    );
  }

  Widget _buildErrorView() {
    return Container(
      width: widget.width,
      height: widget.playingHeight ?? 300,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              _errorMessage ?? 'An error occurred',
              //style: const TextStyle(color: Colors.white, fontSize: 14),
              style: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
                color: Colors.white,
                fontSize: _getResponsiveFontSize(context),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                _videoPlayerController?.dispose();
                _videoPlayerController = null;
                _initializeVideo();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1920) {
      return 56.0;
    } else if (screenWidth >= 1440) {
      return 48.0;
    } else if (screenWidth >= 1280) {
      return 40.0;
    } else {
      return 40.0;
    }
  }

  EdgeInsets _getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth >= 1440) {
      return const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0);
    } else if (screenWidth >= 1280) {
      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 3.0);
    } else {
      return const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0);
    }
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1920) {
      return 16.0;
    } else if (screenWidth >= 1440) {
      return 14.0;
    } else {
      return 12.0;
    }
  }

  double _getResponsiveIconSize(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1920) {
      return 24.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 22.0; // Large
    } else if (screenWidth >= 1280) {
      return 21.0; // Medium
    } else if (screenWidth >= 768) {
      return 18.0; // Small
    } else {
      return 16.0; // Extra Small (fallback for very small screens)
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget content;

    switch (_currentState) {
      case VideoWidgetState.defaultState:
      case VideoWidgetState.hover:
        content = _buildDefaultState();
        break;
      case VideoWidgetState.playing:
        content = _buildPlayingState();
        break;
      case VideoWidgetState.maximized:
        content = _buildPlayingState(); // The overlay handles maximized view
        break;
    }

    return CompositedTransformTarget(
      link: _layerLink,
      child: MouseRegion(
        onEnter: (_) => _onHover(true),
        onExit: (_) => _onHover(false),
        child: GestureDetector(
          onTap: _onTap,
          child: Focus(focusNode: _focusNode, child: content),
        ),
      ),
    );
  }
}

double _getResponsiveValueFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 14.0; // Default for very small screens
  }
}
