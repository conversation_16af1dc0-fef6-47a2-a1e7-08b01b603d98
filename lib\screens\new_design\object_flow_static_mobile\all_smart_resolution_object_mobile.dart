import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';

class AllSmartResolutionObjectMobile extends StatefulWidget {
  const AllSmartResolutionObjectMobile({super.key});

  @override
  State<AllSmartResolutionObjectMobile> createState() =>
      _AllSmartResolutionObjectMobileState();
}

class _AllSmartResolutionObjectMobileState
    extends State<AllSmartResolutionObjectMobile> {
  // JSON data for the validation rules
  final Map<String, dynamic> validationData = {
    "header": {
      "title": "All Smart Resolution",
      "bulkApplyButton": "Bulk Apply"
    },
    "validationRules": [
      {
        "id": 1,
        "title": "Email Validation Rules",
        "description":
            "Add IS_VALID_EMAIL and IS_UNIQUE operators for email field",
        "status": "SIMPLE",
        "actionType": "Business Rules",
        "hasCircleIcon": false
      },
      {
        "id": 2,
        "title": "Phone Format Validation",
        "description":
            "Apply MATCHES_PATTERN operator for international phone numbers",
        "status": "MODERATE",
        "actionType": "Business Rules",
        "hasCircleIcon": false
      },
      {
        "id": 3,
        "title": "Customer-Address Relationship",
        "description":
            "Configure one-to-many with CASCADE delete for address cleanup",
        "status": "MODERATE",
        "actionType": "Attribute",
        "hasCircleIcon": false
      },
      {
        "id": 4,
        "title": "Title of the Issue",
        "description": "Description of the Issue",
        "status": "MODERATE",
        "actionType": "Entity Relationship",
        "hasCircleIcon": false
      },
      {
        "id": 5,
        "title": "Address Auto-Complete",
        "description": "Description of the Issue",
        "status": "MODERATE",
        "actionType": "Entity Relationship",
        "hasCircleIcon": false
      }
    ]
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          scrollbars: false,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.auto_fix_high,
            color: Colors.white,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            validationData['header']['title'],
            style: TextStyle(
              fontSize: ResponsiveFontSizes.bodyLarge(context),
              fontWeight: FontWeight.w600,
              color: Colors.black87,
              fontFamily: FontManager.fontFamilyInter,
            ),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: Colors.black,
                width: 1,
              ),
            ),
            child: Text(
              validationData['header']['bulkApplyButton'],
              style: TextStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w500,
                color: Colors.black,
                fontFamily: FontManager.fontFamilyInter,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
