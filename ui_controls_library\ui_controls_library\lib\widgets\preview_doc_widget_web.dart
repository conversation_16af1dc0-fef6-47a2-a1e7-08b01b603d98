import 'dart:convert';
// ignore: deprecated_member_use
import 'dart:html' as html;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'dart:math' as math;
import '../utils/callback_interpreter.dart';

/// Enum for preview document states
enum PreviewDocState {
  defaultState, // Shows filename with 3 icons
  hoverState, // Blue border on hover
  touchState, // Touch interaction
  disabledState, // Grayed out
}

/// Web-specific implementation of PreviewDocWidget
///
/// This widget provides a document preview interface optimized for web browsers with features like:
/// - Mouse hover interactions with border color changes
/// - Web-optimized document preview modal with full browser features
/// - Action icons (eye, navigate, download) with hover states
/// - Responsive design for various screen sizes
/// - Web-specific file handling (blob URLs, new tab opening)
class PreviewDocWidgetImplementation extends StatefulWidget {
  /// The document source URL or path
  final String? documentSource;

  /// The document filename to display
  final String fileName;

  /// Whether the widget is disabled
  final bool isDisabled;

  /// Whether the widget is read-only
  final bool isReadOnly;

  /// The theme color for the preview
  final Color themeColor;

  /// The background color for the preview
  final Color backgroundColor;

  /// The text color for the preview
  final Color textColor;

  /// The border radius for the preview container
  final double borderRadius;

  /// Whether to show a border around the preview
  final bool hasBorder;

  /// The border color for the preview
  final Color borderColor;

  /// The border width for the preview
  final double borderWidth;

  /// Whether to show a shadow under the preview
  final bool hasShadow;

  /// The elevation of the shadow
  final double elevation;

  /// The width of the preview
  final double? width;

  /// The height of the preview
  final double? height;

  /// The padding around the preview
  final EdgeInsetsGeometry padding;

  /// The margin around the preview
  final EdgeInsetsGeometry margin;

  /// Whether to show the eye icon
  final bool showEyeIcon;

  /// Whether to show the navigate icon
  final bool showNavigateIcon;

  /// Whether to show the download icon
  final bool showDownloadIcon;

  /// The font size for the filename text
  final double fontSize;

  /// The font weight for the filename text
  final FontWeight fontWeight;

  /// Whether to use dark theme
  final bool isDarkTheme;

  /// The callback when the eye icon is clicked (preview)
  final VoidCallback? onPreview;

  /// The callback when the navigate icon is clicked (open externally)
  final VoidCallback? onNavigate;

  /// The callback when the download icon is clicked
  final VoidCallback? onDownload;

  /// The callback when the widget is tapped
  final VoidCallback? onTap;

  // Advanced interaction properties
  /// Callback for when the widget is hovered
  final void Function(bool)? onHover;

  /// Callback for when the widget is focused
  final void Function(bool)? onFocus;

  /// Focus node for the widget
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  /// Color to use when the widget is hovered
  final Color? hoverColor;

  /// Color to use when the widget is focused
  final Color? focusColor;

  /// Whether to enable feedback when the widget is interacted with
  final bool enableFeedback;

  /// Callback for when the widget is double-tapped
  final VoidCallback? onDoubleTap;

  /// Callback for when the widget is long-pressed
  final VoidCallback? onLongPress;

  // Accessibility properties
  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Whether to exclude the widget from semantics
  final bool excludeFromSemantics;

  // JSON configuration properties
  /// Callbacks defined in JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use JSON callbacks
  final bool useJsonCallbacks;

  /// State to pass to the callback interpreter
  final Map<String, dynamic>? callbackState;

  /// Custom callback handlers
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration
  final Map<String, dynamic>? jsonConfig;

  /// Whether to use JSON styling
  final bool useJsonStyling;

  /// Whether to use JSON formatting
  final bool useJsonFormatting;

  // Document-specific JSON configuration
  /// Whether to use JSON document configuration
  final bool useJsonDocumentConfig;

  /// Document-specific JSON configuration
  final Map<String, dynamic>? documentConfig;

  /// Creates a web document preview widget.
  const PreviewDocWidgetImplementation({
    super.key,
    this.documentSource,
    this.fileName = 'Member',
    this.isDisabled = false,
    this.isReadOnly = false,
    this.themeColor = const Color(0xFF0058FF),
    this.backgroundColor = Colors.white,
    this.textColor = Colors.black,
    this.borderRadius = 4.0,
    this.hasBorder = true,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.width,
    this.height,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.margin = const EdgeInsets.all(0),
    this.showEyeIcon = true,
    this.showNavigateIcon = true,
    this.showDownloadIcon = true,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.isDarkTheme = false,
    this.onPreview,
    this.onNavigate,
    this.onDownload,
    this.onTap,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.focusNode,
    this.autofocus = false,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onDoubleTap,
    this.onLongPress,
    // Accessibility properties
    this.semanticsLabel,
    this.excludeFromSemantics = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
    this.useJsonStyling = false,
    this.useJsonFormatting = false,
    // Document-specific JSON configuration
    this.useJsonDocumentConfig = false,
    this.documentConfig,
  });

  @override
  State<PreviewDocWidgetImplementation> createState() =>
      _PreviewDocWidgetImplementationState();
}

class _PreviewDocWidgetImplementationState
    extends State<PreviewDocWidgetImplementation> {
  PreviewDocState _currentState = PreviewDocState.defaultState;
  bool _isHovered = false;
  bool _isFocused = false;
  late FocusNode _focusNode;

  // Callback state
  Map<String, dynamic> _callbackState = {};

  // Icon colors
  final Color hoverIconColor = const Color(0xFF0058FF);
  final Color defaultIconColor = const Color(0xFFCCCCCC);

  @override
  void initState() {
    super.initState();

    // Initialize focus node
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);

    // Initialize callback state
    if (widget.callbackState != null) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }

    // Set initial state based on disabled property
    if (widget.isDisabled) {
      _currentState = PreviewDocState.disabledState;
    }
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (widget.onFocus != null) {
        widget.onFocus!(_isFocused);
      }
    });
  }

  void _onHoverChange(bool isHovered) {
    if (widget.isDisabled) return;

    setState(() {
      _isHovered = isHovered;
      _currentState =
          isHovered ? PreviewDocState.hoverState : PreviewDocState.defaultState;
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    });
  }

  @override
  void didUpdateWidget(PreviewDocWidgetImplementation oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update focus node if changed
    if (widget.focusNode != oldWidget.focusNode) {
      _focusNode.removeListener(_onFocusChange);
      _focusNode = widget.focusNode ?? _focusNode;
      _focusNode.addListener(_onFocusChange);
    }

    // Update callback state if provided
    if (widget.callbackState != null &&
        widget.callbackState != oldWidget.callbackState) {
      _callbackState = Map<String, dynamic>.from(widget.callbackState!);
    }

    // Update state based on disabled property
    if (widget.isDisabled != oldWidget.isDisabled) {
      setState(() {
        _currentState =
            widget.isDisabled
                ? PreviewDocState.disabledState
                : PreviewDocState.defaultState;
      });
    }
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  /// Executes a callback defined in JSON
  void _executeJsonCallback(String callbackName, [dynamic value]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    final callback = widget.jsonCallbacks![callbackName];
    if (callback == null) return;

    CallbackInterpreter.executeCallback(
      callback,
      context,
      value: value,
      state: _callbackState,
      customHandlers: widget.customCallbackHandlers,
    );
  }

  /// Shows the document preview dialog optimized for web
  void _showDocumentPreview() {
    if (widget.isDisabled || widget.isReadOnly) return;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return WebDocumentPreviewDialog(
          documentSource: widget.documentSource,
          fileName: widget.fileName,
          themeColor: widget.themeColor,
        );
      },
    );

    // Execute JSON callback if defined
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onPreview')) {
      _executeJsonCallback('onPreview');
    }

    // Call standard callback
    if (widget.onPreview != null) {
      widget.onPreview!();
    }
  }

  /// Opens the document in a new window/tab (web-specific)
  void _openDocument() {
    if (widget.isDisabled || widget.isReadOnly) return;

    try {
      if (widget.documentSource != null && widget.documentSource!.isNotEmpty) {
        // If we have a document source URL, open it directly
        html.window.open(widget.documentSource!, '_blank');
      } else {
        // Create a blob URL and open it in new tab
        final bytes = _generateSamplePdfBytes();
        final blob = html.Blob([bytes]);
        final url = html.Url.createObjectUrlFromBlob(blob);
        html.window.open(url, '_blank');

        // Clean up the blob URL after a delay
        Future.delayed(const Duration(seconds: 5), () {
          html.Url.revokeObjectUrl(url);
        });
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Opened ${widget.fileName} in new tab'),
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to open document: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }

    // Execute JSON callback if defined
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onNavigate')) {
      _executeJsonCallback('onNavigate');
    }

    // Call standard callback
    if (widget.onNavigate != null) {
      widget.onNavigate!();
    }
  }

  // Generate sample PDF bytes for download/navigation
  List<int> _generateSamplePdfBytes() {
    // This is a simple text file for demo purposes
    // In a real implementation, you would generate actual PDF bytes
    final content = '''
${widget.fileName}

Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.

• Morbi viverra semper lorem nec molestie.
• Maecenas tincidunt est efficitur ligula euismod.
• Sit amet ornare est vulputate.

Vestibulum neque massa, scelerisque sit amet ligula eu, congue molestie mi. 
Praesent ut varius sem.

Generated on: ${DateTime.now().toString()}
''';
    return utf8.encode(content);
  }

  /// Downloads the document (web-specific)
  void _downloadDocument() {
    if (widget.isDisabled || widget.isReadOnly) return;

    try {
      // Web download implementation
      final bytes = _generateSamplePdfBytes();
      final blob = html.Blob([bytes]);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor =
          html.document.createElement('a') as html.AnchorElement
            ..href = url
            ..style.display = 'none'
            ..download = widget.fileName;
      html.document.body?.children.add(anchor);
      anchor.click();
      html.document.body?.children.remove(anchor);
      html.Url.revokeObjectUrl(url);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Downloaded ${widget.fileName}'),
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Download failed: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }

    // Execute JSON callback if defined
    if (widget.useJsonCallbacks &&
        widget.jsonCallbacks != null &&
        widget.jsonCallbacks!.containsKey('onDownload')) {
      _executeJsonCallback('onDownload');
    }

    // Call standard callback
    if (widget.onDownload != null) {
      widget.onDownload!();
    }
  }

  /// Gets responsive font size based on screen width
  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return widget.fontSize * 1.125; // 18.0 for 16.0 base
    } else if (screenWidth >= 1440) {
      return widget.fontSize; // 16.0
    } else if (screenWidth >= 1280) {
      return widget.fontSize * 0.875; // 14.0 for 16.0 base
    } else if (screenWidth >= 768) {
      return widget.fontSize * 0.75; // 12.0 for 16.0 base
    } else {
      return widget.fontSize * 0.625; // 10.0 for 16.0 base
    }
  }

  /// Gets responsive icon size based on screen width
  double _getResponsiveIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 24.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 22.0; // Large
    } else if (screenWidth >= 1280) {
      return 20.0; // Medium
    } else if (screenWidth >= 768) {
      return 18.0; // Small
    } else {
      return 18.0; // Extra Small
    }
  }

  /// Gets responsive height based on screen width
  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 56.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 48.0; // Large
    } else if (screenWidth >= 1280) {
      return 40.0; // Medium
    } else if (screenWidth >= 768) {
      return 40.0; // Small
    } else {
      return 40.0; // Default for very small screens
    }
  }

  /// Gets responsive padding based on screen width
  EdgeInsets _getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth >= 1440) {
      return const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0);
    } else if (screenWidth >= 1280) {
      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 3.0);
    } else if (screenWidth >= 768) {
      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 2.0);
    } else {
      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 1.0);
    }
  }

  /// Gets the file icon based on filename extension
  IconData _getFileIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();

    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'txt':
        return Icons.text_snippet;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'svg':
        return Icons.image;
      case 'mp4':
      case 'avi':
      case 'mov':
        return Icons.video_file;
      case 'mp3':
      case 'wav':
      case 'flac':
        return Icons.audio_file;
      case 'zip':
      case 'rar':
      case '7z':
        return Icons.folder_zip;
      default:
        return Icons.insert_drive_file;
    }
  }

  /// Gets the file icon color based on filename extension
  Color _getFileIconColor(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();

    switch (extension) {
      case 'pdf':
        return const Color(0xFFD32F2F); // Red for PDF
      case 'doc':
      case 'docx':
        return const Color(0xFF1976D2); // Blue for Word
      case 'xls':
      case 'xlsx':
        return const Color(0xFF388E3C); // Green for Excel
      case 'ppt':
      case 'pptx':
        return const Color(0xFFFF5722); // Orange for PowerPoint
      case 'txt':
        return const Color(0xFF757575); // Gray for text files
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'svg':
        return const Color(0xFF9C27B0); // Purple for images
      case 'mp4':
      case 'avi':
      case 'mov':
        return const Color(0xFFE91E63); // Pink for videos
      case 'mp3':
      case 'wav':
      case 'flac':
        return const Color(0xFF4CAF50); // Green for audio
      case 'zip':
      case 'rar':
      case '7z':
        return const Color(0xFF795548); // Brown for archives
      default:
        return const Color(0xFF666666); // Default gray
    }
  }

  @override
  Widget build(BuildContext context) {
    final effectiveWidth = widget.width ?? double.infinity;
    final effectiveHeight = widget.height ?? _getResponsiveHeight(context);
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    // Determine border color based on state
    Color borderColor;
    if (_currentState == PreviewDocState.disabledState) {
      borderColor = const Color(0xFFCCCCCC);
    } else if (_currentState == PreviewDocState.hoverState) {
      borderColor = widget.hoverColor ?? widget.themeColor;
    } else if (_isFocused) {
      borderColor = widget.focusColor ?? widget.themeColor;
    } else {
      borderColor = widget.borderColor;
    }

    // Determine icon color based on state
    Color iconColor;
    if (_currentState == PreviewDocState.disabledState) {
      iconColor = Colors.grey.shade400;
    } else if (_isHovered) {
      iconColor = hoverIconColor;
    } else {
      iconColor = defaultIconColor;
    }

    Widget content = Container(
      width: effectiveWidth,
      height: effectiveHeight,
      padding: _getResponsivePadding(context),
      margin: widget.margin,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(widget.borderRadius),
        border:
            widget.hasBorder ? Border.all(color: borderColor, width: 1) : null,
      ),
      child: Row(
        children: [
          // File type icon
          Icon(
            _getFileIcon(widget.fileName),
            size: _getResponsiveIconSize(context),
            color:
                _currentState == PreviewDocState.disabledState
                    ? Colors.grey.shade400
                    : _getFileIconColor(widget.fileName),
          ),
          const SizedBox(width: 8),

          // File name
          Expanded(
            child: Text(
              widget.fileName,
              style: TextStyle(
                fontSize: _getResponsiveFontSize(context),
                fontWeight: widget.fontWeight,
                color:
                    _currentState == PreviewDocState.disabledState
                        ? Colors.grey.shade400
                        : Color(0xFF333333),
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),

          const SizedBox(width: 8),

          // Action icons
          if (widget.showEyeIcon) ...[
            GestureDetector(
              onTap: _showDocumentPreview,
              child: Transform.scale(
                scale: 1,
                child: SvgPicture.asset(
                  _isHovered
                      ? 'assets/images/eye-hover.svg'
                      : 'assets/images/eye.svg',
                  package: 'ui_controls_library',
                  width: _getResponsiveIconSize(context),
                ),
              ),
            ),
            const SizedBox(width: 5),
          ],

          if (widget.showNavigateIcon) ...[
            GestureDetector(
              onTap: _openDocument,
              child: SvgPicture.asset(
                _isHovered
                    ? 'assets/images/maxmize-hover.svg'
                    : 'assets/images/maxmize.svg',
                package: 'ui_controls_library',
                width: _getResponsiveIconSize(context),
              ),
            ),
            const SizedBox(width: 5),
          ],

          if (widget.showDownloadIcon) ...[
            GestureDetector(
              onTap: _downloadDocument,
              child: SvgPicture.asset(
                _isHovered
                    ? 'assets/images/download-hover.svg'
                    : 'assets/images/download.svg',
                package: 'ui_controls_library',
                width: _getResponsiveIconSize(context),
              ),
            ),
          ],
        ],
      ),
    );

    // Apply hover detection for web
    content = MouseRegion(
      onEnter: (_) => _onHoverChange(true),
      onExit: (_) => _onHoverChange(false),
      cursor:
          widget.isDisabled
              ? SystemMouseCursors.forbidden
              : SystemMouseCursors.click,
      child: content,
    );

    // Apply focus handling
    content = Focus(
      focusNode: _focusNode,
      autofocus: widget.autofocus,
      onFocusChange: (hasFocus) => _onFocusChange(),
      child: content,
    );

    // Apply gesture detection
    if (widget.onTap != null ||
        widget.onDoubleTap != null ||
        widget.onLongPress != null) {
      content = GestureDetector(
        onTap:
            widget.onTap != null && !widget.isDisabled
                ? () {
                  // Execute onTap callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onTap')) {
                    _executeJsonCallback('onTap');
                  }

                  // Call standard callback
                  widget.onTap!();
                }
                : null,
        onDoubleTap:
            widget.onDoubleTap != null && !widget.isDisabled
                ? () {
                  // Execute onDoubleTap callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onDoubleTap')) {
                    _executeJsonCallback('onDoubleTap');
                  }

                  // Call standard callback
                  widget.onDoubleTap!();
                }
                : null,
        onLongPress:
            widget.onLongPress != null && !widget.isDisabled
                ? () {
                  // Execute onLongPress callback if defined in JSON
                  if (widget.useJsonCallbacks &&
                      widget.jsonCallbacks != null &&
                      widget.jsonCallbacks!.containsKey('onLongPress')) {
                    _executeJsonCallback('onLongPress');
                  }

                  // Call standard callback
                  widget.onLongPress!();
                }
                : null,
        child: content,
      );
    }

    // Apply accessibility
    if (widget.semanticsLabel != null && !widget.excludeFromSemantics) {
      content = Semantics(
        label: widget.semanticsLabel,
        excludeSemantics: false,
        child: content,
      );
    }

    return content;
  }
}

/// Web Document Preview Dialog for web-optimized document viewing
class WebDocumentPreviewDialog extends StatefulWidget {
  final String? documentSource;
  final String fileName;
  final Color themeColor;

  const WebDocumentPreviewDialog({
    super.key,
    this.documentSource,
    required this.fileName,
    this.themeColor = const Color(0xFF0058FF),
  });

  @override
  State<WebDocumentPreviewDialog> createState() =>
      _WebDocumentPreviewDialogState();
}

class _WebDocumentPreviewDialogState extends State<WebDocumentPreviewDialog> {
  double _zoomLevel = 1.0;
  int _currentPage = 1;
  final int _totalPages = 5; // Simulated total pages
  bool _isLoading = true;
  bool _hasError = false;
  final TextEditingController _searchController = TextEditingController();
  bool _showSearchBar = false;

  @override
  void initState() {
    super.initState();

    // Simulate document loading
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
          // Simulate error if no document source
          if (widget.documentSource == null || widget.documentSource!.isEmpty) {
            _hasError = false; // For demo, we'll show content anyway
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _closeDialog() {
    Navigator.of(context).pop();
  }

  void _zoomIn() {
    setState(() {
      _zoomLevel = math.min(3.0, _zoomLevel + 0.25);
    });
  }

  void _zoomOut() {
    setState(() {
      _zoomLevel = math.max(0.5, _zoomLevel - 0.25);
    });
  }

  void _nextPage() {
    if (_currentPage < _totalPages) {
      setState(() {
        _currentPage++;
      });
    }
  }

  void _previousPage() {
    if (_currentPage > 1) {
      setState(() {
        _currentPage--;
      });
    }
  }

  void _toggleSearch() {
    setState(() {
      _showSearchBar = !_showSearchBar;
      if (!_showSearchBar) {
        _searchController.clear();
      }
    });
  }

  void _downloadDocument() {
    try {
      // Web download implementation
      final bytes = _generateSamplePdfBytes();
      final blob = html.Blob([bytes]);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor =
          html.document.createElement('a') as html.AnchorElement
            ..href = url
            ..style.display = 'none'
            ..download = widget.fileName;
      html.document.body?.children.add(anchor);
      anchor.click();
      html.document.body?.children.remove(anchor);
      html.Url.revokeObjectUrl(url);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Downloaded ${widget.fileName}'),
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Download failed: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  // Generate sample PDF bytes for download
  List<int> _generateSamplePdfBytes() {
    // This is a simple text file for demo purposes
    // In a real implementation, you would generate actual PDF bytes
    final content = '''
${widget.fileName}

Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.

Page $_currentPage of $_totalPages

• Morbi viverra semper lorem nec molestie.
• Maecenas tincidunt est efficitur ligula euismod.
• Sit amet ornare est vulputate.

Vestibulum neque massa, scelerisque sit amet ligula eu, congue molestie mi. 
Praesent ut varius sem.
''';
    return utf8.encode(content);
  }

  Widget _buildWebToolbar() {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Left side - Menu icon and file name
          IconButton(
            icon: const Icon(Icons.menu, size: 18),
            onPressed: () {},
            tooltip: 'Menu',
            color: Colors.grey.shade600,
          ),
          Expanded(
            flex: 3,
            child: Text(
              widget.fileName.length > 12
                  ? '${widget.fileName.substring(0, 12)}...'
                  : widget.fileName,
              style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w400),
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Page navigation
          Text(
            '$_currentPage',
            style: TextStyle(fontSize: 13, color: Colors.grey.shade700),
          ),
          const SizedBox(width: 4),
          Text(
            '/',
            style: TextStyle(fontSize: 13, color: Colors.grey.shade500),
          ),
          const SizedBox(width: 4),
          Text(
            '$_totalPages',
            style: TextStyle(fontSize: 13, color: Colors.grey.shade700),
          ),

          const SizedBox(width: 16),

          // Zoom controls
          IconButton(
            icon: const Icon(Icons.remove, size: 16),
            onPressed: _zoomOut,
            tooltip: 'Zoom Out',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade400),
              borderRadius: BorderRadius.circular(2),
            ),
            child: Text(
              '${(_zoomLevel * 100).round()}%',
              style: const TextStyle(fontSize: 11),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.add, size: 16),
            onPressed: _zoomIn,
            tooltip: 'Zoom In',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),

          const SizedBox(width: 8),

          // Right side actions
          IconButton(
            icon: const Icon(Icons.file_download, size: 18),
            onPressed: _downloadDocument,
            tooltip: 'Download',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
          IconButton(
            icon: const Icon(Icons.print, size: 18),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Print ${widget.fileName}'),
                  duration: const Duration(seconds: 2),
                ),
              );
            },
            tooltip: 'Print',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
          IconButton(
            icon: const Icon(Icons.close, size: 18),
            onPressed: _closeDialog,
            tooltip: 'Close',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
          IconButton(
            icon: const Icon(Icons.more_vert, size: 18),
            onPressed: () {
              // Show more options menu
              _showMoreOptions();
            },
            tooltip: 'More options',
            color: Colors.grey.shade600,
            padding: const EdgeInsets.all(4),
            constraints: const BoxConstraints(minWidth: 24, minHeight: 24),
          ),
          const SizedBox(width: 8),
        ],
      ),
    );
  }

  void _showMoreOptions() {
    showMenu(
      context: context,
      position: RelativeRect.fromLTRB(
        MediaQuery.of(context).size.width - 200,
        50,
        MediaQuery.of(context).size.width,
        100,
      ),
      items: [
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.search, size: 16, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              const Text('Search'),
            ],
          ),
          onTap: _toggleSearch,
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.fullscreen, size: 16, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              const Text('Full Screen'),
            ],
          ),
          onTap: () {
            // Toggle full screen
          },
        ),
        PopupMenuItem(
          child: Row(
            children: [
              Icon(Icons.info_outline, size: 16, color: Colors.grey.shade600),
              const SizedBox(width: 8),
              const Text('Document Info'),
            ],
          ),
          onTap: () {
            // Show document info
          },
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    if (!_showSearchBar) return const SizedBox.shrink();

    return Container(
      height: 40,
      color: Colors.grey.shade100,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'Search in document...',
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                isDense: true,
              ),
              style: const TextStyle(fontSize: 12),
              onSubmitted: (value) {
                // Implement search functionality
              },
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: const Icon(Icons.arrow_upward, size: 16),
            onPressed: () {
              // Previous search result
            },
            tooltip: 'Previous',
          ),
          IconButton(
            icon: const Icon(Icons.arrow_downward, size: 16),
            onPressed: () {
              // Next search result
            },
            tooltip: 'Next',
          ),
          IconButton(
            icon: const Icon(Icons.close, size: 16),
            onPressed: _toggleSearch,
            tooltip: 'Close Search',
          ),
        ],
      ),
    );
  }

  Widget _buildWebDocumentContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              'Failed to load document',
              style: TextStyle(color: Colors.grey.shade700, fontSize: 16),
            ),
          ],
        ),
      );
    }

    return Container(
      color: Colors.grey.shade200,
      child: Center(
        child: Transform.scale(
          scale: _zoomLevel,
          child: Container(
            width: 400,
            height: 500,
            margin: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(51),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Document header
                  Text(
                    widget.fileName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Page $_currentPage',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                  const SizedBox(height: 20),

                  // Document content
                  const Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
                            style: TextStyle(fontSize: 12, height: 1.5),
                          ),
                          SizedBox(height: 16),

                          Text('• Morbi viverra semper lorem nec molestie.'),
                          Text(
                            '• Maecenas tincidunt est efficitur ligula euismod.',
                          ),
                          Text('• Sit amet ornare est vulputate.'),

                          SizedBox(height: 20),

                          Text(
                            'Vestibulum neque massa, scelerisque sit amet ligula eu, congue molestie mi. Praesent ut varius sem.',
                            style: TextStyle(fontSize: 12, height: 1.5),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;

    return AlertDialog(
      contentPadding: EdgeInsets.zero,
      content: Container(
        width: screenSize.width * 0.8,
        height: screenSize.height * 0.8,
        constraints: const BoxConstraints(maxWidth: 800, maxHeight: 600),
        child: Column(
          children: [
            _buildWebToolbar(),
            _buildSearchBar(),
            Expanded(child: _buildWebDocumentContent()),
          ],
        ),
      ),
    );
  }
}
