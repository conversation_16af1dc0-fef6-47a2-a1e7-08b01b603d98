import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';
import 'dart:async';
import 'dart:convert';
import '../utils/callback_interpreter.dart';

/// Format options for the date time display
enum DateTimeFormat {
  /// Standard format (e.g., "Jan 1, 2023 12:30 PM")
  standard,

  /// Short format (e.g., "01/01/2023 12:30")
  short,

  /// Long format (e.g., "January 1, 2023 at 12:30:45 PM")
  long,

  /// ISO format (e.g., "2023-01-01T12:30:45")
  iso,

  /// Custom format (specified by formatPattern)
  custom,
}

/// A comprehensive widget for displaying and selecting date and time.
///
/// This widget provides extensive customization options for displaying
/// and selecting date and time values with various formats and styles.
class DateTimeWidget extends StatefulWidget {
  /// The initial date and time value
  final DateTime? initialDateTime;

  /// Whether to allow selecting a date
  final bool allowDateSelection;

  /// Whether to allow selecting a time
  final bool allowTimeSelection;

  /// Whether to show the date part
  final bool showDate;

  /// Whether to show the time part
  final bool showTime;

  /// Format of the date time display
  final DateTimeFormat format;

  /// Custom format pattern (used when format is DateTimeFormat.custom)
  final String? formatPattern;

  /// Whether to use 24-hour format for time
  final bool use24HourFormat;

  /// Whether to show seconds in time
  final bool showSeconds;

  /// Whether to show AM/PM indicator
  final bool showAmPm;

  /// Whether to show the weekday
  final bool showWeekday;

  /// Whether to show the year
  final bool showYear;

  /// Whether to show the month
  final bool showMonth;

  /// Whether to show the day
  final bool showDay;

  /// Whether to update the current date/time automatically
  final bool autoUpdate;

  /// Update interval in seconds for auto-update
  final int updateIntervalSeconds;

  /// Locale for formatting (e.g., 'en_US', 'fr_FR')
  final String locale;

  /// Text style for the date time display
  final TextStyle? textStyle;

  /// Color of the text
  final Color textColor;

  /// Background color of the widget
  final Color backgroundColor;

  /// Size of the font
  final double fontSize;

  /// Weight of the font
  final FontWeight fontWeight;

  /// Font family
  final String? fontFamily;

  /// Alignment of the text
  final TextAlign textAlign;

  /// Whether to show a border
  final bool hasBorder;

  /// Radius of the border corners
  final double borderRadius;

  /// Color of the border
  final Color borderColor;

  /// Width of the border
  final double borderWidth;

  /// Whether to show a shadow
  final bool hasShadow;

  /// Elevation of the shadow
  final double elevation;

  /// Whether to use a compact layout
  final bool isCompact;

  /// Label text to display
  final String? label;

  /// Prefix text to display
  final String? prefix;

  /// Suffix text to display
  final String? suffix;

  /// Icon to display before the text
  final IconData? prefixIcon;

  /// Icon to display after the text
  final IconData? suffixIcon;

  /// Whether to use dark theme colors
  final bool isDarkTheme;

  /// Whether to show relative time (e.g., "2 hours ago")
  final bool isRelative;

  /// Whether to show as countdown to target date
  final bool isCountdown;

  /// Target date for countdown
  final DateTime? targetDate;

  /// Whether to show an icon
  final bool showIcon;

  /// Icon to display
  final IconData? icon;

  /// Color of the icon
  final Color? iconColor;

  /// Whether to animate the display
  final bool isAnimated;

  /// Custom text to display instead of formatted date/time
  final String? customText;

  /// Whether to convert text to uppercase
  final bool isUpperCase;

  /// Whether to convert text to lowercase
  final bool isLowerCase;

  /// Whether to capitalize the first letter of each word
  final bool isCapitalized;

  /// Whether to display text in italic
  final bool isItalic;

  /// Whether to display text in bold
  final bool isBold;

  /// Whether to underline the text
  final bool isUnderlined;

  /// Padding around the widget
  final double padding;

  /// Width of the widget
  final double? width;

  /// Height of the widget
  final double? height;

  /// Alignment of the main axis
  final MainAxisAlignment mainAxisAlignment;

  /// Alignment of the cross axis
  final CrossAxisAlignment crossAxisAlignment;

  /// Whether to show a clear button
  final bool showClearButton;

  /// Whether to show a now button (sets to current date/time)
  final bool showNowButton;

  /// Whether to show a calendar icon for date selection
  final bool showCalendarIcon;

  /// Whether to show a clock icon for time selection
  final bool showClockIcon;

  /// Whether to use combined date-time picker dialog
  /// When true, only calendar icon is shown and opens a combined picker
  final bool useCombinedDateTimePicker;

  /// Icon to use for calendar
  final IconData calendarIcon;

  /// Icon to use for clock
  final IconData clockIcon;

  /// Color of the calendar icon
  final Color? calendarIconColor;

  /// Color of the clock icon
  final Color? clockIconColor;

  /// Whether to show a separator between date and time
  final bool showSeparator;

  /// Separator text between date and time
  final String separator;

  /// Whether to blink the separator
  final bool blinkSeparator;

  /// Whether to show the time zone
  final bool showTimeZone;

  /// Time zone to display
  final String? timeZone;

  /// Whether to use a digital style display
  final bool digitalStyle;

  /// Whether the widget is enabled
  final bool enabled;

  /// Whether the widget is read-only
  final bool readOnly;

  /// Minimum selectable date
  final DateTime? minDate;

  /// Maximum selectable date
  final DateTime? maxDate;

  /// Callback when date/time changes
  final Function(DateTime)? onChanged;

  /// Callback when date/time is selected
  final Function(DateTime)? onSelected;

  // Advanced interaction properties
  /// Callback for mouse hover
  ///
  /// This function is called when the mouse pointer enters or exits the widget.
  /// The parameter is true when the mouse enters and false when it exits.
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  ///
  /// This function is called when the widget gains or loses keyboard focus.
  /// The parameter is true when focus is gained and false when it is lost.
  final void Function(bool)? onFocus;

  /// Color when the widget is hovered
  final Color? hoverColor;

  /// Color when the widget is focused
  final Color? focusColor;

  /// Whether to enable haptic/audio feedback
  ///
  /// If true, the widget will provide haptic and/or audio feedback when interacted with.
  final bool enableFeedback;

  /// Callback for tap gesture
  ///
  /// This function is called when the widget is tapped.
  final VoidCallback? onTap;

  /// Callback for double tap gesture
  ///
  /// This function is called when the widget is double-tapped.
  final VoidCallback? onDoubleTap;

  /// Callback for long press gesture
  ///
  /// This function is called when the widget is long-pressed.
  final VoidCallback? onLongPress;

  /// Focus node for controlling focus
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  // JSON configuration properties
  /// Dynamic callback definitions from JSON
  ///
  /// This can include callback definitions for various events:
  /// - onChanged: Executed when the date/time changes
  /// - onSelected: Executed when a date/time is selected
  /// - onTap: Executed when the widget is tapped
  /// - onDoubleTap: Executed when the widget is double-tapped
  /// - onLongPress: Executed when the widget is long-pressed
  /// - onHover: Executed when the mouse enters or exits the widget
  /// - onFocus: Executed when the widget gains or loses focus
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use dynamic callbacks from JSON
  final bool useJsonCallbacks;

  /// State map for dynamic callbacks
  ///
  /// This map can be used by dynamic callbacks to store and retrieve state.
  final Map<String, dynamic>? callbackState;

  /// Custom handlers for dynamic callbacks
  ///
  /// This map contains custom handler functions that can be called by dynamic callbacks.
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration for the widget
  ///
  /// This allows for full configuration of the widget through a JSON object.
  final Map<String, dynamic>? jsonConfig;

  /// Creates a date time widget.
  const DateTimeWidget({
    super.key,
    this.initialDateTime,
    this.allowDateSelection = true,
    this.allowTimeSelection = true,
    this.showDate = true,
    this.showTime = true,
    this.format = DateTimeFormat.standard,
    this.formatPattern,
    this.use24HourFormat = false,
    this.showSeconds = false,
    this.showAmPm = true,
    this.showWeekday = false,
    this.showYear = true,
    this.showMonth = true,
    this.showDay = true,
    this.autoUpdate = false,
    this.updateIntervalSeconds = 1,
    this.locale = 'en_US',
    this.textStyle,
    this.textColor = const Color(0xFF333333),
    this.backgroundColor = Colors.white,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.fontFamily,
    this.textAlign = TextAlign.start,
    this.hasBorder = true,
    this.borderRadius = 4.0,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isCompact = false,
    this.label,
    this.prefix,
    this.suffix,
    this.prefixIcon,
    this.suffixIcon,
    this.isDarkTheme = false,
    this.isRelative = false,
    this.isCountdown = false,
    this.targetDate,
    this.showIcon = false,
    this.icon,
    this.iconColor,
    this.isAnimated = false,
    this.customText,
    this.isUpperCase = false,
    this.isLowerCase = false,
    this.isCapitalized = false,
    this.isItalic = false,
    this.isBold = false,
    this.isUnderlined = false,
    this.padding = 16.0,
    this.width,
    this.height,
    this.mainAxisAlignment = MainAxisAlignment.center,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.showClearButton = false,
    this.showNowButton = false,
    this.showCalendarIcon = true,
    this.showClockIcon = true,
    this.useCombinedDateTimePicker = false,
    this.calendarIcon = Icons.calendar_today,
    this.clockIcon = Icons.access_time,
    this.calendarIconColor,
    this.clockIconColor,
    this.showSeparator = true,
    this.separator = " at ",
    this.blinkSeparator = false,
    this.showTimeZone = false,
    this.timeZone,
    this.digitalStyle = false,
    this.enabled = true,
    this.readOnly = false,
    this.minDate,
    this.maxDate,
    this.onChanged,
    this.onSelected,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.hoverColor,
    this.focusColor,
    this.enableFeedback = true,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.focusNode,
    this.autofocus = false,
    // JSON configuration properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
  });

  /// Creates a DateTimeWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the DateTimeWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "initialDateTime": "2023-01-01T12:30:00",
  ///   "format": "standard",
  ///   "showWeekday": true,
  ///   "textColor": "blue",
  ///   "fontSize": 18,
  ///   "hasBorder": true
  /// }
  /// ```
  factory DateTimeWidget.fromJson(Map<String, dynamic> json) {
    // Parse date format
    DateTimeFormat format = DateTimeFormat.standard;
    if (json['format'] != null) {
      switch (json['format'].toString().toLowerCase()) {
        case 'short':
          format = DateTimeFormat.short;
          break;
        case 'long':
          format = DateTimeFormat.long;
          break;
        case 'iso':
          format = DateTimeFormat.iso;
          break;
        case 'custom':
          format = DateTimeFormat.custom;
          break;
        case 'standard':
        default:
          format = DateTimeFormat.standard;
          break;
      }
    }

    // Parse text alignment
    TextAlign textAlign = TextAlign.start;
    if (json['textAlign'] != null) {
      switch (json['textAlign'].toString().toLowerCase()) {
        case 'center':
          textAlign = TextAlign.center;
          break;
        case 'left':
        case 'start':
          textAlign = TextAlign.left;
          break;
        case 'right':
        case 'end':
          textAlign = TextAlign.right;
          break;
        case 'justify':
          textAlign = TextAlign.justify;
          break;
      }
    }

    // Parse font weight
    FontWeight fontWeight = FontWeight.normal;
    if (json['fontWeight'] != null) {
      if (json['fontWeight'] == 'bold' || json['fontWeight'] == true) {
        fontWeight = FontWeight.bold;
      } else if (json['fontWeight'] == 'light') {
        fontWeight = FontWeight.w300;
      } else if (json['fontWeight'] is int) {
        final weight = json['fontWeight'] as int;
        switch (weight) {
          case 100:
            fontWeight = FontWeight.w100;
            break;
          case 200:
            fontWeight = FontWeight.w200;
            break;
          case 300:
            fontWeight = FontWeight.w300;
            break;
          case 400:
            fontWeight = FontWeight.w400;
            break;
          case 500:
            fontWeight = FontWeight.w500;
            break;
          case 600:
            fontWeight = FontWeight.w600;
            break;
          case 700:
            fontWeight = FontWeight.w700;
            break;
          case 800:
            fontWeight = FontWeight.w800;
            break;
          case 900:
            fontWeight = FontWeight.w900;
            break;
        }
      }
    }

    // Parse main axis alignment
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.center;
    if (json['mainAxisAlignment'] != null) {
      switch (json['mainAxisAlignment'].toString().toLowerCase()) {
        case 'start':
          mainAxisAlignment = MainAxisAlignment.start;
          break;
        case 'end':
          mainAxisAlignment = MainAxisAlignment.end;
          break;
        case 'spaceBetween':
          mainAxisAlignment = MainAxisAlignment.spaceBetween;
          break;
        case 'spaceAround':
          mainAxisAlignment = MainAxisAlignment.spaceAround;
          break;
        case 'spaceEvenly':
          mainAxisAlignment = MainAxisAlignment.spaceEvenly;
          break;
        case 'center':
        default:
          mainAxisAlignment = MainAxisAlignment.center;
          break;
      }
    }

    // Parse cross axis alignment
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center;
    if (json['crossAxisAlignment'] != null) {
      switch (json['crossAxisAlignment'].toString().toLowerCase()) {
        case 'start':
          crossAxisAlignment = CrossAxisAlignment.start;
          break;
        case 'end':
          crossAxisAlignment = CrossAxisAlignment.end;
          break;
        case 'stretch':
          crossAxisAlignment = CrossAxisAlignment.stretch;
          break;
        case 'baseline':
          crossAxisAlignment = CrossAxisAlignment.baseline;
          break;
        case 'center':
        default:
          crossAxisAlignment = CrossAxisAlignment.center;
          break;
      }
    }

    // Parse icons
    IconData? prefixIcon;
    if (json['prefixIcon'] != null) {
      prefixIcon = _getIconData(json['prefixIcon'].toString());
    }

    IconData? suffixIcon;
    if (json['suffixIcon'] != null) {
      suffixIcon = _getIconData(json['suffixIcon'].toString());
    }

    IconData? icon;
    if (json['icon'] != null) {
      icon = _getIconData(json['icon'].toString());
    }

    IconData calendarIcon = Icons.calendar_today;
    if (json['calendarIcon'] != null) {
      calendarIcon =
          _getIconData(json['calendarIcon'].toString()) ?? Icons.calendar_today;
    }

    IconData clockIcon = Icons.access_time;
    if (json['clockIcon'] != null) {
      clockIcon =
          _getIconData(json['clockIcon'].toString()) ?? Icons.access_time;
    }

    // Parse colors
    Color? textColor;
    if (json['textColor'] != null) {
      textColor = _colorFromJson(json['textColor']);
    }

    Color? backgroundColor;
    if (json['backgroundColor'] != null) {
      backgroundColor = _colorFromJson(json['backgroundColor']);
    }

    Color? borderColor;
    if (json['borderColor'] != null) {
      borderColor = _colorFromJson(json['borderColor']);
    }

    Color? iconColor;
    if (json['iconColor'] != null) {
      iconColor = _colorFromJson(json['iconColor']);
    }

    Color? calendarIconColor;
    if (json['calendarIconColor'] != null) {
      calendarIconColor = _colorFromJson(json['calendarIconColor']);
    }

    Color? clockIconColor;
    if (json['clockIconColor'] != null) {
      clockIconColor = _colorFromJson(json['clockIconColor']);
    }

    Color? hoverColor;
    if (json['hoverColor'] != null) {
      hoverColor = _colorFromJson(json['hoverColor']);
    }

    Color? focusColor;
    if (json['focusColor'] != null) {
      focusColor = _colorFromJson(json['focusColor']);
    }

    // Parse date
    DateTime? initialDateTime;
    if (json['initialDateTime'] != null) {
      try {
        initialDateTime = DateTime.parse(json['initialDateTime'].toString());
      } catch (e) {
        // Silently handle the error
      }
    }

    DateTime? minDate;
    if (json['minDate'] != null) {
      try {
        minDate = DateTime.parse(json['minDate'].toString());
      } catch (e) {
        // Silently handle the error
      }
    }

    DateTime? maxDate;
    if (json['maxDate'] != null) {
      try {
        maxDate = DateTime.parse(json['maxDate'].toString());
      } catch (e) {
        // Silently handle the error
      }
    }

    DateTime? targetDate;
    if (json['targetDate'] != null) {
      try {
        targetDate = DateTime.parse(json['targetDate'].toString());
      } catch (e) {
        // Silently handle the error
      }
    }

    // Parse JSON callback properties
    Map<String, dynamic>? jsonCallbacks;
    bool useJsonCallbacks = json['useJsonCallbacks'] as bool? ?? false;

    if (json['callbacks'] != null) {
      if (json['callbacks'] is Map) {
        jsonCallbacks = Map<String, dynamic>.from(json['callbacks'] as Map);
        useJsonCallbacks = true;
      } else if (json['callbacks'] is String) {
        try {
          jsonCallbacks =
              jsonDecode(json['callbacks'] as String) as Map<String, dynamic>;
          useJsonCallbacks = true;
        } catch (e) {
          // Silently handle the error
        }
      }
    }

    // Parse additional callback properties for specific events
    if (json['onChanged'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onChanged'] = json['onChanged'];
      useJsonCallbacks = true;
    }

    if (json['onSelected'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onSelected'] = json['onSelected'];
      useJsonCallbacks = true;
    }

    if (json['onTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onTap'] = json['onTap'];
      useJsonCallbacks = true;
    }

    if (json['onDoubleTap'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onDoubleTap'] = json['onDoubleTap'];
      useJsonCallbacks = true;
    }

    if (json['onLongPress'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onLongPress'] = json['onLongPress'];
      useJsonCallbacks = true;
    }

    if (json['onHover'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onHover'] = json['onHover'];
      useJsonCallbacks = true;
    }

    if (json['onFocus'] != null) {
      jsonCallbacks ??= {};
      jsonCallbacks['onFocus'] = json['onFocus'];
      useJsonCallbacks = true;
    }

    // Create text style if provided
    TextStyle? textStyle;
    if (json['textStyle'] != null && json['textStyle'] is Map) {
      final Map<String, dynamic> styleMap = Map<String, dynamic>.from(
        json['textStyle'] as Map,
      );

      Color? styleColor;
      if (styleMap['color'] != null) {
        styleColor = _colorFromJson(styleMap['color']);
      }

      FontWeight? styleFontWeight;
      if (styleMap['fontWeight'] != null) {
        if (styleMap['fontWeight'] == 'bold') {
          styleFontWeight = FontWeight.bold;
        } else if (styleMap['fontWeight'] == 'normal') {
          styleFontWeight = FontWeight.normal;
        } else if (styleMap['fontWeight'] == 'light') {
          styleFontWeight = FontWeight.w300;
        }
      }

      FontStyle? styleFontStyle;
      if (styleMap['fontStyle'] != null) {
        if (styleMap['fontStyle'] == 'italic') {
          styleFontStyle = FontStyle.italic;
        } else if (styleMap['fontStyle'] == 'normal') {
          styleFontStyle = FontStyle.normal;
        }
      }

      TextDecoration? styleDecoration;
      if (styleMap['decoration'] != null) {
        if (styleMap['decoration'] == 'underline') {
          styleDecoration = TextDecoration.underline;
        } else if (styleMap['decoration'] == 'lineThrough') {
          styleDecoration = TextDecoration.lineThrough;
        } else if (styleMap['decoration'] == 'overline') {
          styleDecoration = TextDecoration.overline;
        } else if (styleMap['decoration'] == 'none') {
          styleDecoration = TextDecoration.none;
        }
      }

      textStyle = TextStyle(
        color: styleColor,
        fontSize:
            styleMap['fontSize'] != null
                ? (styleMap['fontSize'] as num).toDouble()
                : null,
        fontWeight: styleFontWeight,
        fontStyle: styleFontStyle,
        fontFamily: styleMap['fontFamily'] as String?,
        decoration: styleDecoration,
      );
    }

    return DateTimeWidget(
      initialDateTime: initialDateTime,
      allowDateSelection: json['allowDateSelection'] as bool? ?? true,
      allowTimeSelection: json['allowTimeSelection'] as bool? ?? true,
      showDate: json['showDate'] as bool? ?? true,
      showTime: json['showTime'] as bool? ?? true,
      format: format,
      formatPattern: json['formatPattern'] as String?,
      use24HourFormat: json['use24HourFormat'] as bool? ?? false,
      showSeconds: json['showSeconds'] as bool? ?? false,
      showAmPm: json['showAmPm'] as bool? ?? true,
      showWeekday: json['showWeekday'] as bool? ?? false,
      showYear: json['showYear'] as bool? ?? true,
      showMonth: json['showMonth'] as bool? ?? true,
      showDay: json['showDay'] as bool? ?? true,
      autoUpdate: json['autoUpdate'] as bool? ?? false,
      updateIntervalSeconds: json['updateIntervalSeconds'] as int? ?? 1,
      locale: json['locale'] as String? ?? 'en_US',
      textStyle: textStyle,
      textColor: textColor ?? const Color(0xFF333333),
      backgroundColor: backgroundColor ?? Colors.white,
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 16.0,
      fontWeight: fontWeight,
      fontFamily: json['fontFamily'] as String?,
      textAlign: textAlign,
      hasBorder: json['hasBorder'] as bool? ?? true,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      borderColor: borderColor ?? const Color(0xFFCCCCCC),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation: (json['elevation'] as num?)?.toDouble() ?? 2.0,
      isCompact: json['isCompact'] as bool? ?? false,
      label: json['label'] as String?,
      prefix: json['prefix'] as String?,
      suffix: json['suffix'] as String?,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      isRelative: json['isRelative'] as bool? ?? false,
      isCountdown: json['isCountdown'] as bool? ?? false,
      targetDate: targetDate,
      showIcon: json['showIcon'] as bool? ?? false,
      icon: icon,
      iconColor: iconColor,
      isAnimated: json['isAnimated'] as bool? ?? false,
      customText: json['customText'] as String?,
      isUpperCase: json['isUpperCase'] as bool? ?? false,
      isLowerCase: json['isLowerCase'] as bool? ?? false,
      isCapitalized: json['isCapitalized'] as bool? ?? false,
      isItalic: json['isItalic'] as bool? ?? false,
      isBold: json['isBold'] as bool? ?? false,
      isUnderlined: json['isUnderlined'] as bool? ?? false,
      padding: (json['padding'] as num?)?.toDouble() ?? 16.0,
      width: (json['width'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      showClearButton: json['showClearButton'] as bool? ?? false,
      showNowButton: json['showNowButton'] as bool? ?? false,
      showCalendarIcon: json['showCalendarIcon'] as bool? ?? true,
      showClockIcon: json['showClockIcon'] as bool? ?? true,
      useCombinedDateTimePicker:
          json['useCombinedDateTimePicker'] as bool? ?? false,
      calendarIcon: calendarIcon,
      clockIcon: clockIcon,
      calendarIconColor: calendarIconColor,
      clockIconColor: clockIconColor,
      showSeparator: json['showSeparator'] as bool? ?? true,
      separator: json['separator'] as String? ?? " at ",
      blinkSeparator: json['blinkSeparator'] as bool? ?? false,
      showTimeZone: json['showTimeZone'] as bool? ?? false,
      timeZone: json['timeZone'] as String?,
      digitalStyle: json['digitalStyle'] as bool? ?? false,
      enabled: json['enabled'] as bool? ?? true,
      readOnly: json['readOnly'] as bool? ?? false,
      minDate: minDate,
      maxDate: maxDate,
      // Advanced interaction properties
      onHover: null, // Can't be serialized
      onFocus: null, // Can't be serialized
      hoverColor: hoverColor,
      focusColor: focusColor,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
      onTap: null, // Can't be serialized
      onDoubleTap: null, // Can't be serialized
      onLongPress: null, // Can't be serialized
      focusNode: null, // Can't be serialized
      autofocus: json['autofocus'] as bool? ?? false,
      // JSON callback properties
      jsonCallbacks: jsonCallbacks,
      useJsonCallbacks: useJsonCallbacks,
      callbackState: {},
      jsonConfig: json,
    );
  }

  /// Converts a JSON color value to a Flutter Color
  ///
  /// Accepts hex strings (e.g., "#FF0000"), color names (e.g., "red"),
  /// or integer values (e.g., 0xFFFF0000)
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          // Silently handle the error and return null
          return null;
        }
      }

      // Handle named colors
      switch (colorValue.toLowerCase()) {
        case 'red':
          return Colors.red;
        case 'blue':
          return Colors.blue;
        case 'green':
          return Colors.green;
        case 'yellow':
          return Colors.yellow;
        case 'orange':
          return Colors.orange;
        case 'purple':
          return Colors.purple;
        case 'pink':
          return Colors.pink;
        case 'brown':
          return Colors.brown;
        case 'grey':
        case 'gray':
          return Colors.grey;
        case 'black':
          return Colors.black;
        case 'white':
          return Colors.white;
        case 'amber':
          return Colors.amber;
        case 'cyan':
          return Colors.cyan;
        case 'indigo':
          return Colors.indigo;
        case 'lime':
          return Colors.lime;
        case 'teal':
          return Colors.teal;
        default:
          return null;
      }
    } else if (colorValue is int) {
      // Handle integer color values
      return Color(colorValue);
    }

    return null;
  }

  /// Gets an IconData from a string name
  static IconData? _getIconData(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'calendar':
      case 'calendar_today':
        return Icons.calendar_today;
      case 'calendar_month':
        return Icons.calendar_month;
      case 'event':
        return Icons.event;
      case 'date_range':
        return Icons.date_range;
      case 'access_time':
      case 'clock':
        return Icons.access_time;
      case 'watch':
      case 'watch_later':
        return Icons.watch_later;
      case 'schedule':
        return Icons.schedule;
      case 'timer':
        return Icons.timer;
      case 'clear':
        return Icons.clear;
      case 'close':
        return Icons.close;
      case 'cancel':
        return Icons.cancel;
      case 'delete':
        return Icons.delete;
      case 'remove':
        return Icons.remove;
      case 'add':
        return Icons.add;
      case 'edit':
        return Icons.edit;
      case 'check':
        return Icons.check;
      case 'done':
        return Icons.done;
      case 'info':
        return Icons.info;
      case 'help':
        return Icons.help;
      case 'warning':
        return Icons.warning;
      case 'error':
        return Icons.error;
      default:
        return null;
    }
  }

  /// Converts the widget configuration to a JSON map
  ///
  /// This method allows for serializing the widget's configuration to JSON,
  /// which can be useful for saving configurations or sending them to a server.
  Map<String, dynamic> toJson() {
    // Convert date format to string
    String formatString;
    if (format == DateTimeFormat.short) {
      formatString = 'short';
    } else if (format == DateTimeFormat.long) {
      formatString = 'long';
    } else if (format == DateTimeFormat.iso) {
      formatString = 'iso';
    } else if (format == DateTimeFormat.custom) {
      formatString = 'custom';
    } else {
      formatString = 'standard'; // Default for DateTimeFormat.standard
    }

    // Convert text alignment to string
    String textAlignString;
    if (textAlign == TextAlign.center) {
      textAlignString = 'center';
    } else if (textAlign == TextAlign.left) {
      textAlignString = 'left';
    } else if (textAlign == TextAlign.right) {
      textAlignString = 'right';
    } else if (textAlign == TextAlign.justify) {
      textAlignString = 'justify';
    } else {
      textAlignString = 'start';
    }

    // Convert main axis alignment to string
    String mainAxisAlignmentString;
    if (mainAxisAlignment == MainAxisAlignment.start) {
      mainAxisAlignmentString = 'start';
    } else if (mainAxisAlignment == MainAxisAlignment.end) {
      mainAxisAlignmentString = 'end';
    } else if (mainAxisAlignment == MainAxisAlignment.spaceBetween) {
      mainAxisAlignmentString = 'spaceBetween';
    } else if (mainAxisAlignment == MainAxisAlignment.spaceAround) {
      mainAxisAlignmentString = 'spaceAround';
    } else if (mainAxisAlignment == MainAxisAlignment.spaceEvenly) {
      mainAxisAlignmentString = 'spaceEvenly';
    } else {
      mainAxisAlignmentString = 'center';
    }

    // Convert cross axis alignment to string
    String crossAxisAlignmentString;
    if (crossAxisAlignment == CrossAxisAlignment.start) {
      crossAxisAlignmentString = 'start';
    } else if (crossAxisAlignment == CrossAxisAlignment.end) {
      crossAxisAlignmentString = 'end';
    } else if (crossAxisAlignment == CrossAxisAlignment.stretch) {
      crossAxisAlignmentString = 'stretch';
    } else if (crossAxisAlignment == CrossAxisAlignment.baseline) {
      crossAxisAlignmentString = 'baseline';
    } else {
      crossAxisAlignmentString = 'center';
    }

    // Convert font weight to int
    int fontWeightValue;
    if (fontWeight == FontWeight.w100) {
      fontWeightValue = 100;
    } else if (fontWeight == FontWeight.w200) {
      fontWeightValue = 200;
    } else if (fontWeight == FontWeight.w300) {
      fontWeightValue = 300;
    } else if (fontWeight == FontWeight.w400 ||
        fontWeight == FontWeight.normal) {
      fontWeightValue = 400;
    } else if (fontWeight == FontWeight.w500) {
      fontWeightValue = 500;
    } else if (fontWeight == FontWeight.w600) {
      fontWeightValue = 600;
    } else if (fontWeight == FontWeight.w700 || fontWeight == FontWeight.bold) {
      fontWeightValue = 700;
    } else if (fontWeight == FontWeight.w800) {
      fontWeightValue = 800;
    } else if (fontWeight == FontWeight.w900) {
      fontWeightValue = 900;
    } else {
      fontWeightValue = 400;
    }

    // Create the JSON map
    final Map<String, dynamic> json = {
      'format': formatString,
      'formatPattern': formatPattern,
      'initialDateTime': initialDateTime?.toIso8601String(),
      'allowDateSelection': allowDateSelection,
      'allowTimeSelection': allowTimeSelection,
      'showDate': showDate,
      'showTime': showTime,
      'use24HourFormat': use24HourFormat,
      'showSeconds': showSeconds,
      'showAmPm': showAmPm,
      'showWeekday': showWeekday,
      'showYear': showYear,
      'showMonth': showMonth,
      'showDay': showDay,
      'autoUpdate': autoUpdate,
      'updateIntervalSeconds': updateIntervalSeconds,
      'locale': locale,
      'textColor': _colorToHex(textColor),
      'backgroundColor': _colorToHex(backgroundColor),
      'fontSize': fontSize,
      'fontWeight': fontWeightValue,
      'fontFamily': fontFamily,
      'textAlign': textAlignString,
      'hasBorder': hasBorder,
      'borderRadius': borderRadius,
      'borderColor': _colorToHex(borderColor),
      'borderWidth': borderWidth,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'isCompact': isCompact,
      'label': label,
      'prefix': prefix,
      'suffix': suffix,
      'isDarkTheme': isDarkTheme,
      'isRelative': isRelative,
      'isCountdown': isCountdown,
      'targetDate': targetDate?.toIso8601String(),
      'showIcon': showIcon,
      'iconColor': _colorToHex(iconColor),
      'isAnimated': isAnimated,
      'customText': customText,
      'isUpperCase': isUpperCase,
      'isLowerCase': isLowerCase,
      'isCapitalized': isCapitalized,
      'isItalic': isItalic,
      'isBold': isBold,
      'isUnderlined': isUnderlined,
      'padding': padding,
      'width': width,
      'height': height,
      'mainAxisAlignment': mainAxisAlignmentString,
      'crossAxisAlignment': crossAxisAlignmentString,
      'showClearButton': showClearButton,
      'showNowButton': showNowButton,
      'showCalendarIcon': showCalendarIcon,
      'showClockIcon': showClockIcon,
      'useCombinedDateTimePicker': useCombinedDateTimePicker,
      'showSeparator': showSeparator,
      'separator': separator,
      'blinkSeparator': blinkSeparator,
      'showTimeZone': showTimeZone,
      'timeZone': timeZone,
      'digitalStyle': digitalStyle,
      'enabled': enabled,
      'readOnly': readOnly,
      'minDate': minDate?.toIso8601String(),
      'maxDate': maxDate?.toIso8601String(),
      'hoverColor': _colorToHex(hoverColor),
      'focusColor': _colorToHex(focusColor),
      'enableFeedback': enableFeedback,
      'autofocus': autofocus,
      'useJsonCallbacks': useJsonCallbacks,
    };

    // Add callbacks if they exist
    if (jsonCallbacks != null && jsonCallbacks!.isNotEmpty) {
      json['callbacks'] = jsonCallbacks;
    }

    return json;
  }

  /// Converts a Color to a hex string
  static String? _colorToHex(Color? color) {
    if (color == null) return null;

    // Convert to hex format using the modern API
    final r = (color.r * 255).round();
    final g = (color.g * 255).round();
    final b = (color.b * 255).round();
    return '#${r.toRadixString(16).padLeft(2, '0')}${g.toRadixString(16).padLeft(2, '0')}${b.toRadixString(16).padLeft(2, '0')}';
  }

  @override
  State<DateTimeWidget> createState() => _DateTimeWidgetState();
}

class _DateTimeWidgetState extends State<DateTimeWidget>
    with SingleTickerProviderStateMixin {
  late DateTime _selectedDateTime;
  String _formattedDateTime = '';
  bool _separatorVisible = true;
  Timer? _updateTimer;
  Timer? _blinkTimer;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _isHovered = false;
  bool _hasFocus = false;
  void _onHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }
    });
  }

  /// Handles focus state changes
  void _onFocusChange(bool hasFocus) {
    setState(() {
      _hasFocus = hasFocus;
      if (widget.onFocus != null) {
        widget.onFocus!(hasFocus);
      }
    });
  }

  // Map to store dynamic state for callbacks
  Map<String, dynamic> _callbackState = {};

  // Map to store parsed configuration from JSON
  Map<String, dynamic>? _parsedJsonConfig;

  @override
  void initState() {
    super.initState();
    _selectedDateTime = widget.initialDateTime ?? DateTime.now();

    // Initialize callback state
    _callbackState =
        widget.callbackState != null
            ? Map<String, dynamic>.from(widget.callbackState!)
            : {};

    // Parse JSON configuration if provided
    if (widget.jsonConfig != null) {
      _parsedJsonConfig = Map<String, dynamic>.from(widget.jsonConfig!);
    }

    _updateFormattedDateTime();

    // Set up animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.6, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    if (widget.isAnimated) {
      _animationController.repeat(reverse: true);
    }

    // Set up timer for auto-update
    if (widget.autoUpdate) {
      _updateTimer = Timer.periodic(
        Duration(seconds: widget.updateIntervalSeconds),
        (timer) {
          setState(() {
            if (!widget.isRelative && !widget.isCountdown) {
              _selectedDateTime = DateTime.now();
            }
            _updateFormattedDateTime();
          });
        },
      );
    }

    // Set up timer for blinking separator
    if (widget.blinkSeparator && widget.showDate && widget.showTime) {
      _blinkTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
        setState(() {
          _separatorVisible = !_separatorVisible;
        });
      });
    }

    // Execute onInit callback if defined in JSON
    _executeJsonCallback('onInit');
  }

  /// Executes a callback defined in JSON
  ///
  /// This method interprets and executes a callback defined in the JSON configuration.
  /// It supports various callback types and provides access to the current state.
  void _executeJsonCallback(String callbackType, [dynamic data]) {
    if (!widget.useJsonCallbacks || widget.jsonCallbacks == null) return;

    // Check if the callback exists in the JSON configuration
    if (widget.jsonCallbacks!.containsKey(callbackType)) {
      final callback = widget.jsonCallbacks![callbackType];

      // Update the callback state with the current date/time
      _callbackState['currentDateTime'] = _selectedDateTime.toIso8601String();

      // If data is provided, prepare it for the callback
      dynamic callbackValue;
      if (data != null) {
        if (data is DateTime) {
          callbackValue = data.toIso8601String();
          _callbackState['data'] = callbackValue;
        } else {
          callbackValue = data;
          _callbackState['data'] = data.toString();
        }
      } else {
        callbackValue = _selectedDateTime.toIso8601String();
      }

      // Execute the callback using the CallbackInterpreter
      try {
        // Import the CallbackInterpreter class
        // This assumes there's a CallbackInterpreter class available
        // that can execute callbacks defined in JSON
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: callbackValue,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      } catch (e) {
        debugPrint('Error executing JSON callback: $e');
      }
    }
  }

  /// Applies JSON configuration to the widget state
  ///
  /// This method applies any dynamic configuration from the JSON to the widget state.
  /// It allows for runtime changes to the widget's appearance and behavior.
  void _applyJsonConfig() {
    if (_parsedJsonConfig == null) return;

    // Example: Apply dynamic date format changes
    if (_parsedJsonConfig!.containsKey('dynamicFormat')) {
      final formatStr = _parsedJsonConfig!['dynamicFormat'].toString();
      // Update the formatted date with the dynamic format
      try {
        final formatter = DateFormat(formatStr, widget.locale);
        _formattedDateTime = formatter.format(_selectedDateTime);
        setState(() {});
      } catch (e) {
        debugPrint('Error applying dynamic format: $e');
      }
    }

    // Apply other dynamic configurations as needed
    setState(() {
      // State will be updated with any changes made
    });
  }

  @override
  void dispose() {
    _updateTimer?.cancel();
    _blinkTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  void _updateFormattedDateTime() {
    if (widget.customText != null) {
      _formattedDateTime = widget.customText!;
      return;
    }

    if (widget.isRelative) {
      _formattedDateTime = _getRelativeTime(_selectedDateTime);
      return;
    }

    if (widget.isCountdown && widget.targetDate != null) {
      _formattedDateTime = _getCountdownTime(widget.targetDate!);
      return;
    }

    // Use custom format if provided
    if (widget.format == DateTimeFormat.custom &&
        widget.formatPattern != null) {
      try {
        _formattedDateTime = DateFormat(
          widget.formatPattern!,
          widget.locale,
        ).format(_selectedDateTime);
      } catch (e) {
        _formattedDateTime = 'Invalid format';
      }
      return;
    }

    // Use predefined formats
    String datePattern = '';
    String timePattern = '';

    // Build date pattern
    if (widget.showDate) {
      switch (widget.format) {
        case DateTimeFormat.short:
          datePattern = 'yMd';
          break;
        case DateTimeFormat.long:
          List<String> dateParts = [];
          if (widget.showWeekday) dateParts.add('EEEE');
          dateParts.add('MMMM d, y');
          datePattern = dateParts.join(', ');
          break;
        case DateTimeFormat.iso:
          datePattern = 'yyyy-MM-dd';
          break;
        case DateTimeFormat.standard:
        default:
          List<String> dateParts = [];
          if (widget.showWeekday) dateParts.add('EEE');

          if (widget.showMonth && widget.showDay && widget.showYear) {
            dateParts.add('MMM d, y');
          } else {
            List<String> components = [];
            if (widget.showMonth) components.add('MMM');
            if (widget.showDay) components.add('d');
            if (widget.showYear) components.add('y');
            dateParts.add(components.join(' '));
          }

          datePattern = dateParts.join(', ');
          break;
      }
    }

    // Build time pattern
    if (widget.showTime) {
      switch (widget.format) {
        case DateTimeFormat.short:
          timePattern = widget.use24HourFormat ? 'HH:mm' : 'h:mm a';
          break;
        case DateTimeFormat.long:
          timePattern =
              widget.use24HourFormat
                  ? (widget.showSeconds ? 'HH:mm:ss' : 'HH:mm')
                  : (widget.showSeconds ? 'h:mm:ss a' : 'h:mm a');
          break;
        case DateTimeFormat.iso:
          timePattern = widget.showSeconds ? 'HH:mm:ss' : 'HH:mm';
          break;
        case DateTimeFormat.standard:
        default:
          timePattern =
              widget.use24HourFormat
                  ? (widget.showSeconds ? 'HH:mm:ss' : 'HH:mm')
                  : (widget.showSeconds ? 'h:mm:ss a' : 'h:mm a');

          if (!widget.showAmPm && !widget.use24HourFormat) {
            timePattern = timePattern.replaceAll(' a', '');
          }
          break;
      }

      if (widget.showTimeZone && widget.timeZone != null) {
        timePattern += ' (${widget.timeZone})';
      }
    }

    // Combine date and time patterns
    String fullPattern = '';
    if (widget.showDate && widget.showTime) {
      if (widget.format == DateTimeFormat.iso) {
        fullPattern = '${datePattern}T$timePattern';
      } else {
        fullPattern = '$datePattern${widget.separator}$timePattern';
      }
    } else if (widget.showDate) {
      fullPattern = datePattern;
    } else if (widget.showTime) {
      fullPattern = timePattern;
    }

    try {
      _formattedDateTime = DateFormat(
        fullPattern,
        widget.locale,
      ).format(_selectedDateTime);
    } catch (e) {
      _formattedDateTime = 'Invalid format';
    }

    // Apply text transformations
    if (widget.isUpperCase) {
      _formattedDateTime = _formattedDateTime.toUpperCase();
    } else if (widget.isLowerCase) {
      _formattedDateTime = _formattedDateTime.toLowerCase();
    } else if (widget.isCapitalized) {
      _formattedDateTime = _capitalizeWords(_formattedDateTime);
    }
  }

  String _capitalizeWords(String text) {
    if (text.isEmpty) return text;
    return text
        .split(' ')
        .map(
          (word) =>
              word.isNotEmpty
                  ? '${word[0].toUpperCase()}${word.substring(1)}'
                  : '',
        )
        .join(' ');
  }

  String _getRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      final minutes = difference.inMinutes;
      return '$minutes ${minutes == 1 ? 'minute' : 'minutes'} ago';
    } else if (difference.inHours < 24) {
      final hours = difference.inHours;
      return '$hours ${hours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inDays < 30) {
      final days = difference.inDays;
      return '$days ${days == 1 ? 'day' : 'days'} ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months ${months == 1 ? 'month' : 'months'} ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years ${years == 1 ? 'year' : 'years'} ago';
    }
  }

  String _getCountdownTime(DateTime targetDate) {
    final now = DateTime.now();
    final difference = targetDate.difference(now);

    if (difference.isNegative) {
      return 'Expired';
    }

    if (difference.inDays > 0) {
      final days = difference.inDays;
      final hours = difference.inHours % 24;
      return '$days ${days == 1 ? 'day' : 'days'} and $hours ${hours == 1 ? 'hour' : 'hours'} remaining';
    } else if (difference.inHours > 0) {
      final hours = difference.inHours;
      final minutes = difference.inMinutes % 60;
      return '$hours ${hours == 1 ? 'hour' : 'hours'} and $minutes ${minutes == 1 ? 'minute' : 'minutes'} remaining';
    } else if (difference.inMinutes > 0) {
      final minutes = difference.inMinutes;
      final seconds = difference.inSeconds % 60;
      return '$minutes ${minutes == 1 ? 'minute' : 'minutes'} and $seconds ${seconds == 1 ? 'second' : 'seconds'} remaining';
    } else {
      final seconds = difference.inSeconds;
      return '$seconds ${seconds == 1 ? 'second' : 'seconds'} remaining';
    }
  }

  Future<void> _selectDateTime() async {
    if (!widget.enabled || widget.readOnly) return;

    // Execute onBeforeSelectDate callback if defined in JSON
    _executeJsonCallback('onBeforeSelectDate');

    // Show Date Picker
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedDateTime,
      firstDate: widget.minDate ?? DateTime(1900),
      lastDate: widget.maxDate ?? DateTime(2100),
      builder: (context, child) {
        // return Theme(
        //   data: ThemeData(
        //     // useMaterial3: false,
        //     colorScheme: ColorScheme.light(
        //       primary: const Color(0xFF0058FF),
        //       onBackground: const Color(0xFFCCCCCC),
        //     ),
        //   ),
        //   child: child ?? SizedBox(),
        // );
        return Theme(
          data: ThemeData(
            colorScheme: ColorScheme.light(
              primary: const Color(0xFF0058FF),
              onBackground: const Color(0xFFCCCCCC),
            ),
            textTheme: TextTheme(
              bodyMedium: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
              ),
            ),
          ),
          child: child ?? const SizedBox(),
        );
      },
    );

    if (pickedDate == null) {
      _executeJsonCallback('onDateCanceled');
      return;
    }

    // Execute onBeforeSelectTime callback
    _executeJsonCallback('onBeforeSelectTime');

    // Show Time Picker
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay(
        hour: _selectedDateTime.hour,
        minute: _selectedDateTime.minute,
      ),
      builder: (BuildContext context, Widget? child) {
        return MediaQuery(
          data: MediaQuery.of(
            context,
          ).copyWith(alwaysUse24HourFormat: widget.use24HourFormat),
          child: child!,
        );
      },
    );

    if (pickedTime == null) {
      _executeJsonCallback('onTimeCanceled');
      return;
    }

    // If both date and time are selected, update
    final DateTime combinedDateTime = DateTime(
      pickedDate.year,
      pickedDate.month,
      pickedDate.day,
      pickedTime.hour,
      pickedTime.minute,
      _selectedDateTime.second,
    );

    setState(() {
      _selectedDateTime = combinedDateTime;
      _updateFormattedDateTime();
    });

    // Call standard callbacks
    widget.onChanged?.call(_selectedDateTime);
    widget.onSelected?.call(_selectedDateTime);

    // Execute JSON callbacks
    _executeJsonCallback('onChanged', _selectedDateTime);
    _executeJsonCallback('onDateSelected', pickedDate);
    _executeJsonCallback('onTimeSelected', {
      'hour': pickedTime.hour,
      'minute': pickedTime.minute,
      'format': widget.use24HourFormat ? '24h' : '12h',
      'period': pickedTime.period.name,
    });

    _applyJsonConfig();
  }

  Future<void> _selectDate() async {
    if (!widget.enabled || widget.readOnly || !widget.allowDateSelection)
      return;

    // Execute onBeforeSelectDate callback if defined in JSON
    _executeJsonCallback('onBeforeSelectDate');

    // Apply date picker configuration from JSON config if available
    if (_parsedJsonConfig != null &&
        _parsedJsonConfig!.containsKey('datePickerConfig')) {
      // This is where you would apply custom configurations to the date picker
      // Not implemented in this example for simplicity
    }

    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedDateTime,
      firstDate: widget.minDate ?? DateTime(1900),
      lastDate: widget.maxDate ?? DateTime(2100),
    );

    if (pickedDate != null && pickedDate != _selectedDateTime) {
      setState(() {
        _selectedDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          _selectedDateTime.hour,
          _selectedDateTime.minute,
          _selectedDateTime.second,
        );
        _updateFormattedDateTime();
      });

      // Call standard callbacks
      if (widget.onChanged != null) {
        widget.onChanged!(_selectedDateTime);
      }

      if (widget.onSelected != null) {
        widget.onSelected!(_selectedDateTime);
      }

      // Execute JSON callbacks
      _executeJsonCallback('onChanged', _selectedDateTime);
      _executeJsonCallback('onDateSelected', pickedDate);

      // Apply any dynamic JSON configuration
      _applyJsonConfig();
    } else {
      // Execute onDateCanceled callback if defined in JSON
      _executeJsonCallback('onDateCanceled');
    }
  }

  Future<void> _selectTime() async {
    if (!widget.enabled || widget.readOnly || !widget.allowTimeSelection)
      return;

    // Execute onBeforeSelectTime callback if defined in JSON
    _executeJsonCallback('onBeforeSelectTime');

    // Apply time picker configuration from JSON config if available
    if (_parsedJsonConfig != null &&
        _parsedJsonConfig!.containsKey('timePickerConfig')) {
      // This is where you would apply custom configurations to the time picker
      // Not implemented in this example for simplicity
    }

    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay(
        hour: _selectedDateTime.hour,
        minute: _selectedDateTime.minute,
      ),
      builder: (BuildContext context, Widget? child) {
        return MediaQuery(
          data: MediaQuery.of(
            context,
          ).copyWith(alwaysUse24HourFormat: widget.use24HourFormat),
          child: child!,
        );
      },
    );

    if (pickedTime != null) {
      setState(() {
        _selectedDateTime = DateTime(
          _selectedDateTime.year,
          _selectedDateTime.month,
          _selectedDateTime.day,
          pickedTime.hour,
          pickedTime.minute,
          _selectedDateTime.second,
        );
        _updateFormattedDateTime();
      });

      // Call standard callbacks
      if (widget.onChanged != null) {
        widget.onChanged!(_selectedDateTime);
      }

      if (widget.onSelected != null) {
        widget.onSelected!(_selectedDateTime);
      }

      // Execute JSON callbacks
      _executeJsonCallback('onChanged', _selectedDateTime);
      _executeJsonCallback('onTimeSelected', {
        'hour': pickedTime.hour,
        'minute': pickedTime.minute,
        'format': widget.use24HourFormat ? '24h' : '12h',
        'period': pickedTime.period.name,
      });

      // Apply any dynamic JSON configuration
      _applyJsonConfig();
    } else {
      // Execute onTimeCanceled callback if defined in JSON
      _executeJsonCallback('onTimeCanceled');
    }
  }

  void _clearDateTime() {
    if (!widget.enabled || widget.readOnly) return;

    // Execute onBeforeClear callback if defined in JSON
    _executeJsonCallback('onBeforeClear');

    setState(() {
      _selectedDateTime = DateTime.now();
      _updateFormattedDateTime();
    });

    // Call standard callback
    if (widget.onChanged != null) {
      widget.onChanged!(_selectedDateTime);
    }

    // Execute JSON callbacks
    _executeJsonCallback('onChanged', _selectedDateTime);
    _executeJsonCallback('onClear', _selectedDateTime);

    // Apply any dynamic JSON configuration
    _applyJsonConfig();
  }

  void _setNow() {
    if (!widget.enabled || widget.readOnly) return;

    // Execute onBeforeNow callback if defined in JSON
    _executeJsonCallback('onBeforeNow');

    setState(() {
      _selectedDateTime = DateTime.now();
      _updateFormattedDateTime();
    });

    // Call standard callback
    if (widget.onChanged != null) {
      widget.onChanged!(_selectedDateTime);
    }

    // Execute JSON callbacks
    _executeJsonCallback('onChanged', _selectedDateTime);
    _executeJsonCallback('onNow', _selectedDateTime);

    // Apply any dynamic JSON configuration
    _applyJsonConfig();
  }

  /// Shows a combined date and time picker dialog
  Future<void> _showCombinedDateTimePicker() async {
    if (!widget.enabled || widget.readOnly) return;

    // Execute onBeforeCombinedPicker callback if defined in JSON
    _executeJsonCallback('onBeforeCombinedPicker');

    DateTime tempSelectedDate = _selectedDateTime;
    TimeOfDay tempSelectedTime = TimeOfDay.fromDateTime(_selectedDateTime);

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: Text(
                'Select Date & Time',
                // style: TextStyle(
                //   color: widget.isDarkTheme ? Colors.white : Colors.black87,
                //   fontSize: 18,
                //   fontWeight: FontWeight.w600,
                // ),
                style: FontManager.getCustomStyle(
                  fontFamily: FontManager.fontFamilyInter,
                  fontWeight: FontManager.bold,
                  color: widget.isDarkTheme ? Colors.white : Colors.black87,
                  fontSize: _getResponsiveValueFontSize(context),
                ),
              ),
              backgroundColor:
                  widget.isDarkTheme ? Colors.grey.shade800 : Colors.white,
              content: SizedBox(
                width: 320,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Date Section
                    Row(
                      children: [
                        Icon(
                          Icons.calendar_today,
                          color:
                              widget.calendarIconColor ??
                              (widget.isDarkTheme
                                  ? Colors.white70
                                  : Colors.black54),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Date:',
                          // style: TextStyle(
                          //   color:
                          //       widget.isDarkTheme
                          //           ? Colors.white70
                          //           : Colors.black87,
                          //   fontSize: 16,
                          //   fontWeight: FontWeight.w500,
                          // ),
                          style: FontManager.getCustomStyle(
                            fontFamily: FontManager.fontFamilyInter,
                            fontWeight: FontManager.medium,
                            color:
                                widget.isDarkTheme
                                    ? Colors.white70
                                    : Colors.black87,
                            fontSize: _getResponsiveValueFontSize(context),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            DateFormat('MMM d, y').format(tempSelectedDate),
                            // style: TextStyle(
                            //   color:
                            //       widget.isDarkTheme
                            //           ? Colors.white
                            //           : Colors.black,
                            //   fontSize: 16,
                            // ),
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontWeight: FontManager.medium,
                              color:
                                  widget.isDarkTheme
                                      ? Colors.white
                                      : Colors.black87,
                              fontSize: _getResponsiveValueFontSize(context),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    // Date picker button
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton(
                        onPressed: () async {
                          final DateTime? pickedDate = await showDatePicker(
                            context: context,
                            initialDate: tempSelectedDate,
                            firstDate: widget.minDate ?? DateTime(1900),
                            lastDate: widget.maxDate ?? DateTime(2100),
                          );
                          if (pickedDate != null) {
                            setDialogState(() {
                              tempSelectedDate = pickedDate;
                            });
                          }
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor:
                              widget.isDarkTheme
                                  ? Colors.white70
                                  : Colors.black87,
                          side: BorderSide(
                            color:
                                widget.isDarkTheme
                                    ? Colors.white30
                                    : Colors.grey,
                          ),
                        ),
                        child: Text(
                          'Change Date',
                          style: FontManager.getCustomStyle(
                            fontFamily: FontManager.fontFamilyInter,
                            fontWeight: FontManager.medium,
                            fontSize: _getResponsiveValueFontSize(context),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Time Section
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          color:
                              widget.clockIconColor ??
                              (widget.isDarkTheme
                                  ? Colors.white70
                                  : Colors.black54),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Time:',

                          style: FontManager.getCustomStyle(
                            fontFamily: FontManager.fontFamilyInter,
                            fontWeight: FontManager.medium,
                            color:
                                widget.isDarkTheme
                                    ? Colors.white70
                                    : Colors.black87,
                            fontSize: _getResponsiveValueFontSize(context),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            widget.use24HourFormat
                                ? '${tempSelectedTime.hour.toString().padLeft(2, '0')}:${tempSelectedTime.minute.toString().padLeft(2, '0')}'
                                : tempSelectedTime.format(context),

                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontWeight: FontManager.medium,
                              color:
                                  widget.isDarkTheme
                                      ? Colors.white
                                      : Colors.black87,
                              fontSize: _getResponsiveValueFontSize(context),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    // Time picker button
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton(
                        onPressed: () async {
                          final TimeOfDay? pickedTime = await showTimePicker(
                            context: context,
                            initialTime: tempSelectedTime,
                            builder: (BuildContext context, Widget? child) {
                              return MediaQuery(
                                data: MediaQuery.of(context).copyWith(
                                  alwaysUse24HourFormat: widget.use24HourFormat,
                                ),
                                child: child!,
                              );
                            },
                          );
                          if (pickedTime != null) {
                            setDialogState(() {
                              tempSelectedTime = pickedTime;
                            });
                          }
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor:
                              widget.isDarkTheme
                                  ? Colors.white70
                                  : Colors.black87,
                          side: BorderSide(
                            color:
                                widget.isDarkTheme
                                    ? Colors.white30
                                    : Colors.grey,
                          ),
                        ),
                        child: Text(
                          'Change Time',
                          style: FontManager.getCustomStyle(
                            fontFamily: FontManager.fontFamilyInter,
                            fontWeight: FontManager.medium,

                            fontSize: _getResponsiveValueFontSize(context),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Preview Section
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color:
                            widget.isDarkTheme
                                ? Colors.grey.shade700
                                : Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color:
                              widget.isDarkTheme
                                  ? Colors.white30
                                  : Colors.grey.shade300,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Preview:',
                            // style: TextStyle(
                            //   color:
                            //       widget.isDarkTheme
                            //           ? Colors.white70
                            //           : Colors.black54,
                            //   fontSize: 14,
                            //   fontWeight: FontWeight.w500,
                            // ),
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontWeight: FontManager.medium,
                              color:
                                  widget.isDarkTheme
                                      ? Colors.white70
                                      : Colors.black87,
                              fontSize: _getResponsiveValueFontSize(context),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            DateFormat(
                              'MMM d, y${widget.separator}${widget.use24HourFormat ? 'HH:mm' : 'h:mm a'}',
                            ).format(
                              DateTime(
                                tempSelectedDate.year,
                                tempSelectedDate.month,
                                tempSelectedDate.day,
                                tempSelectedTime.hour,
                                tempSelectedTime.minute,
                              ),
                            ),
                            // style: TextStyle(
                            //   color:
                            //       widget.isDarkTheme
                            //           ? Colors.white
                            //           : Colors.black,
                            //   fontSize: 16,
                            //   fontWeight: FontWeight.w600,
                            // ),
                            style: FontManager.getCustomStyle(
                              fontFamily: FontManager.fontFamilyInter,
                              fontWeight: FontManager.medium,
                              color:
                                  widget.isDarkTheme
                                      ? Colors.white
                                      : Colors.black87,
                              fontSize: _getResponsiveValueFontSize(context),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(null);
                  },
                  style: TextButton.styleFrom(
                    foregroundColor:
                        widget.isDarkTheme ? Colors.white70 : Colors.black54,
                  ),
                  child: Text(
                    'Cancel',
                    style: FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontWeight: FontManager.medium,

                      fontSize: _getResponsiveValueFontSize(context),
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(
                      context,
                    ).pop({'date': tempSelectedDate, 'time': tempSelectedTime});
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        widget.isDarkTheme ? Colors.blue.shade700 : Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: Text(
                    'OK',
                    style: FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontWeight: FontManager.medium,
                      fontSize: _getResponsiveValueFontSize(context),
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );

    if (result != null) {
      final DateTime selectedDate = result['date'] as DateTime;
      final TimeOfDay selectedTime = result['time'] as TimeOfDay;

      setState(() {
        _selectedDateTime = DateTime(
          selectedDate.year,
          selectedDate.month,
          selectedDate.day,
          selectedTime.hour,
          selectedTime.minute,
          _selectedDateTime.second,
        );
        _updateFormattedDateTime();
      });

      // Call standard callbacks
      if (widget.onChanged != null) {
        widget.onChanged!(_selectedDateTime);
      }

      if (widget.onSelected != null) {
        widget.onSelected!(_selectedDateTime);
      }

      // Execute JSON callbacks
      _executeJsonCallback('onChanged', _selectedDateTime);
      _executeJsonCallback('onCombinedPickerSelected', {
        'date': selectedDate.toIso8601String(),
        'time': {
          'hour': selectedTime.hour,
          'minute': selectedTime.minute,
          'format': widget.use24HourFormat ? '24h' : '12h',
          'period': selectedTime.period.name,
        },
      });

      // Apply any dynamic JSON configuration
      _applyJsonConfig();
    } else {
      // Execute onCombinedPickerCanceled callback if defined in JSON
      _executeJsonCallback('onCombinedPickerCanceled');
    }
  }

  double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }

  @override
  Widget build(BuildContext context) {
    // Create text style
    TextStyle effectiveTextStyle = TextStyle(
      color: widget.isDarkTheme ? Colors.white : widget.textColor,
      fontSize: _getResponsiveValueFontSize(context),

      //widget.fontSize,
      fontWeight: widget.isBold ? FontWeight.bold : widget.fontWeight,
      fontStyle: widget.isItalic ? FontStyle.italic : FontStyle.normal,
      fontFamily: widget.fontFamily,
      decoration:
          widget.isUnderlined ? TextDecoration.underline : TextDecoration.none,
    );

    if (widget.textStyle != null) {
      effectiveTextStyle = widget.textStyle!.copyWith(
        color: widget.isDarkTheme ? Colors.white : widget.textColor,
        fontWeight: widget.isBold ? FontWeight.bold : widget.fontWeight,
        fontStyle: widget.isItalic ? FontStyle.italic : FontStyle.normal,
        decoration:
            widget.isUnderlined
                ? TextDecoration.underline
                : TextDecoration.none,
      );
    }

    // Create digital style if needed
    if (widget.digitalStyle) {
      // effectiveTextStyle = effectiveTextStyle.copyWith(
      //   fontFamily: 'Courier',
      //   letterSpacing: 2.0,
      //   fontWeight: FontWeight.bold,
      // );
      effectiveTextStyle = FontManager.getCustomStyle(
        fontFamily: FontManager.fontFamilyInter,
        fontWeight: FontManager.bold,
        fontSize: _getResponsiveValueFontSize(context),
      );
    }

    // Build the date time display
    Widget dateTimeText = Text(
      _formattedDateTime,

      style: effectiveTextStyle,
      textAlign: widget.textAlign,
      maxLines: 1, // ✅ no wrapping
      overflow: TextOverflow.ellipsis, // ✅ show ...
      softWrap: false,
    );

    // Apply animation if enabled
    if (widget.isAnimated) {
      dateTimeText = FadeTransition(
        opacity: _fadeAnimation,
        child: dateTimeText,
      );
    }

    // Build the main content
    List<Widget> contentWidgets = [];

    // Add prefix icon if specified
    if (widget.prefixIcon != null) {
      contentWidgets.add(
        Icon(
          widget.prefixIcon,
          color: widget.isDarkTheme ? Colors.white : widget.textColor,
          size: widget.fontSize * 1.2,
        ),
      );
      contentWidgets.add(const SizedBox(width: 8));
    }

    // Add prefix text if specified
    if (widget.prefix != null) {
      contentWidgets.add(
        Text(
          widget.prefix!,
          // style: effectiveTextStyle.copyWith(
          //   fontWeight: FontWeight.normal,
          //   fontStyle: FontStyle.normal,
          // ),
          style: FontManager.getCustomStyle(
            fontFamily: FontManager.fontFamilyInter,
            fontWeight: FontManager.medium,
            fontSize: _getResponsiveValueFontSize(context),
          ),
        ),
      );
      contentWidgets.add(const SizedBox(width: 4));
    }

    // Add the main date time text
    contentWidgets.add(Flexible(child: dateTimeText));

    // Add suffix text if specified
    if (widget.suffix != null) {
      contentWidgets.add(const SizedBox(width: 4));
      contentWidgets.add(
        Text(
          widget.suffix!,
          // style: effectiveTextStyle.copyWith(
          //   fontWeight: FontWeight.normal,
          //   fontStyle: FontStyle.normal,
          // ),
          style: FontManager.getCustomStyle(
            fontFamily: FontManager.fontFamilyInter,
            fontWeight: FontManager.medium,
            fontSize: _getResponsiveValueFontSize(context),
          ),
        ),
      );
    }

    // Add suffix icon if specified
    if (widget.suffixIcon != null) {
      contentWidgets.add(const SizedBox(width: 8));
      contentWidgets.add(
        Icon(
          widget.suffixIcon,
          color: widget.isDarkTheme ? Colors.white : widget.textColor,
          size: widget.fontSize * 1.2,
        ),
      );
    }

    // Add selection icons if enabled
    if (widget.useCombinedDateTimePicker) {
      // Combined picker mode: show only calendar icon that opens combined dialog
      if (widget.showCalendarIcon &&
          widget.enabled &&
          !widget.readOnly &&
          (widget.allowDateSelection || widget.allowTimeSelection)) {
        contentWidgets.add(const SizedBox(width: 8));
        contentWidgets.add(
          IconButton(
            icon: Icon(widget.calendarIcon),
            color:
                widget.calendarIconColor ??
                (widget.isDarkTheme ? Colors.white70 : Colors.black54),
            onPressed: _showCombinedDateTimePicker,
            tooltip: 'Select date & time',
            iconSize: widget.fontSize * 1.2,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        );
      }
    } else {
      // Separate picker mode: show individual icons
      if (widget.showDate &&
          widget.allowDateSelection &&
          widget.showCalendarIcon &&
          widget.enabled &&
          !widget.readOnly) {
        contentWidgets.add(const SizedBox(width: 8));
        contentWidgets.add(
          InkWell(
            onTap: () {
              // Your onClick action here
              // _showCombinedDateTimePicker();
              _selectDateTime();
            },
            child: SvgPicture.asset(
              _isHovered
                  ? 'assets/images/date-hover.svg'
                  : 'assets/images/date.svg',
              package: 'ui_controls_library',
              //width: _getResponsiveIconSize(context),
            ),
          ),
          // IconButton(
          //   icon: Icon(widget.calendarIcon),
          //   color: widget.calendarIconColor ?? (widget.isDarkTheme ? Colors.white70 : Colors.black54),
          //   onPressed: _selectDateTime,
          //   tooltip: 'Select date',
          //   iconSize: widget.fontSize * 1.2,
          //   padding: EdgeInsets.zero,
          //   constraints: const BoxConstraints(),
          // ),
        );
      }

      // if (widget.showTime && widget.allowTimeSelection && widget.showClockIcon && widget.enabled && !widget.readOnly) {
      //   contentWidgets.add(const SizedBox(width: 8));
      //   contentWidgets.add(
      //     IconButton(
      //       icon: Icon(widget.clockIcon),
      //       color: widget.clockIconColor ?? (widget.isDarkTheme ? Colors.white70 : Colors.black54),
      //       onPressed: _selectTime,
      //       tooltip: 'Select time',
      //       iconSize: widget.fontSize * 1.2,
      //       padding: EdgeInsets.zero,
      //       constraints: const BoxConstraints(),
      //     ),
      //   );
      // }
    }

    // Add clear button if enabled
    if (widget.showClearButton && widget.enabled && !widget.readOnly) {
      contentWidgets.add(const SizedBox(width: 8));
      contentWidgets.add(
        IconButton(
          icon: const Icon(Icons.clear),
          color: widget.isDarkTheme ? Colors.white70 : Colors.black54,
          onPressed: _clearDateTime,
          tooltip: 'Clear',
          iconSize: widget.fontSize * 1.2,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      );
    }

    // Add now button if enabled
    if (widget.showNowButton && widget.enabled && !widget.readOnly) {
      contentWidgets.add(const SizedBox(width: 8));
      contentWidgets.add(
        IconButton(
          icon: const Icon(Icons.refresh),
          color: widget.isDarkTheme ? Colors.white70 : Colors.black54,
          onPressed: _setNow,
          tooltip: 'Set to now',
          iconSize: widget.fontSize * 1.2,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      );
    }

    // Create the row with all content
    Widget contentRow = Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      //widget.mainAxisAlignment,
      crossAxisAlignment: widget.crossAxisAlignment,
      children: contentWidgets,
    );

    // Add label if specified
    if (widget.label != null) {
      contentRow = Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.label!,
            // style: TextStyle(
            //   color: widget.isDarkTheme ? Colors.white70 : Colors.black87,
            //   fontSize: widget.fontSize * 0.8,
            // ),
            style: FontManager.getCustomStyle(
              fontFamily: FontManager.fontFamilyInter,
              fontWeight: FontManager.medium,
              color: widget.isDarkTheme ? Colors.white70 : Colors.black87,
              fontSize: _getResponsiveValueFontSize(context) * 8,
            ),
          ),
          const SizedBox(height: 4),
          contentRow,
        ],
      );
    }

    // Create the container with styling
    Widget container = MouseRegion(
      onEnter: (_) => _onHoverChange(true),
      onExit: (_) => _onHoverChange(false),
      cursor: SystemMouseCursors.click,
      child: Container(
        width: double.infinity,
        height: _getResponsiveHeight(context),
        // widget.height,
        padding: _getResponsivePadding(context),
        decoration: BoxDecoration(
          // color:Colors.red,
          color:
              widget.isDarkTheme
                  ? Colors.grey.shade800
                  : widget.backgroundColor,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          border: Border.all(
            color:
                _isHovered
                    ? (widget.hoverColor ?? const Color(0xFF0058FF))
                    : _hasFocus
                    ? (widget.focusColor ?? const Color(0xFF0058FF))
                    : widget.borderColor,
            width: widget.borderWidth,
          ),
          boxShadow:
              widget.hasShadow
                  ? [
                    BoxShadow(
                      color: Colors.black.withAlpha(26), // 0.1 opacity
                      blurRadius: widget.elevation,
                      offset: Offset(0, widget.elevation / 2),
                    ),
                  ]
                  : null,
        ),
        child: contentRow,
      ),
    );

    // Apply compact mode if enabled
    if (widget.isCompact) {
      container = Container(
        padding: _getResponsivePadding(context),
        decoration: BoxDecoration(
          color:
              widget.isDarkTheme
                  ? Colors.grey.shade800
                  : widget.backgroundColor,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          border:
              widget.hasBorder
                  ? Border.all(
                    color: widget.borderColor,
                    width: widget.borderWidth,
                  )
                  : null,
        ),
        child: contentRow,
      );
    }

    // Apply advanced interaction properties
    return MouseRegion(
      onEnter: (event) {
        if (widget.onHover != null) {
          widget.onHover!(true);
        }

        // Execute JSON callback for hover
        if (widget.useJsonCallbacks &&
            widget.jsonCallbacks != null &&
            widget.jsonCallbacks!.containsKey('onHover')) {
          _executeJsonCallback('onHover', true);
        }
      },
      onExit: (event) {
        if (widget.onHover != null) {
          widget.onHover!(false);
        }

        // Execute JSON callback for hover
        if (widget.useJsonCallbacks &&
            widget.jsonCallbacks != null &&
            widget.jsonCallbacks!.containsKey('onHover')) {
          _executeJsonCallback('onHover', false);
        }
      },
      cursor:
          widget.enabled && !widget.readOnly
              ? SystemMouseCursors.click
              : SystemMouseCursors.basic,
      child: Focus(
        focusNode: widget.focusNode,
        autofocus: widget.autofocus,
        onFocusChange: (hasFocus) {
          if (widget.onFocus != null) {
            widget.onFocus!(hasFocus);
          }

          // Execute JSON callback for focus
          if (widget.useJsonCallbacks &&
              widget.jsonCallbacks != null &&
              widget.jsonCallbacks!.containsKey('onFocus')) {
            _executeJsonCallback('onFocus', hasFocus);
          }
        },
        child: GestureDetector(
          onTap:
              widget.enabled && !widget.readOnly
                  ? () {
                    if (widget.onTap != null) {
                      widget.onTap!();
                    }

                    // Execute JSON callback for tap
                    if (widget.useJsonCallbacks &&
                        widget.jsonCallbacks != null &&
                        widget.jsonCallbacks!.containsKey('onTap')) {
                      _executeJsonCallback('onTap');
                    }
                  }
                  : null,
          onDoubleTap:
              widget.enabled && !widget.readOnly
                  ? () {
                    if (widget.onDoubleTap != null) {
                      widget.onDoubleTap!();
                    }

                    // Execute JSON callback for double tap
                    if (widget.useJsonCallbacks &&
                        widget.jsonCallbacks != null &&
                        widget.jsonCallbacks!.containsKey('onDoubleTap')) {
                      _executeJsonCallback('onDoubleTap');
                    }
                  }
                  : null,
          onLongPress:
              widget.enabled && !widget.readOnly
                  ? () {
                    if (widget.onLongPress != null) {
                      widget.onLongPress!();
                    }

                    // Execute JSON callback for long press
                    if (widget.useJsonCallbacks &&
                        widget.jsonCallbacks != null &&
                        widget.jsonCallbacks!.containsKey('onLongPress')) {
                      _executeJsonCallback('onLongPress');
                    }
                  }
                  : null,
          child: container,
        ),
      ),
    );
  }
}

double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 40.0; // Small (768-1024px)
  } else {
    return 40.0; // Default for very small screens
  }
}

double _getResponsiveIconSize(BuildContext context) {
  final double screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth > 1920) {
    return 22.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 20.0; // Large
  } else if (screenWidth >= 1280) {
    return 18.0; // Medium
  } else if (screenWidth >= 768) {
    return 16.0; // Small
  } else {
    return 14.0; // Extra Small (fallback for very small screens)
  }
}

EdgeInsets _getResponsivePadding(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth >= 1440) {
    return const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 4.0,
    ); // Extra Large
  } else if (screenWidth >= 1280) {
    return const EdgeInsets.symmetric(
      horizontal: 12.0,
      vertical: 3.0,
    ); // Large// Large
  } else if (screenWidth >= 768) {
    return const EdgeInsets.symmetric(
      horizontal: 8.0,
      vertical: 2.0,
    ); // Medium// Medium
  } else {
    return const EdgeInsets.symmetric(
      horizontal: 6.0,
      vertical: 1.0,
    ); // Default for very small screens
  }
}
