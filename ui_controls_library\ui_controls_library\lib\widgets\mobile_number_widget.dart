// Enhanced MobileNumberWidget with full international validation and spam protection.
// (This is a continuation of the original widget you provided, with enhancements in _validate method.)
// Ensure to import necessary Flutter packages when integrating this into your app.
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';
import 'dart:convert';
import '../providers/mobile_number_provider.dart';

/// A customizable widget for inputting and displaying mobile phone numbers.
///
/// This widget provides a rich set of customization options for mobile number input,
/// including country code selection, formatting, validation, and styling options.
class MobileNumberWidget extends StatefulWidget {
  /// The initial value of the mobile number input.
  final String? initialValue;

  /// The country code to use (e.g., '+1', '+44', '+91').
  final String? countryCode;

  /// Whether to show the country code.
  final bool showCountryCode;

  /// Whether to allow changing the country code.
  final bool allowCountryCodeChange;

  /// JSON data to display in the widget.
  /// This allows the widget to display any structured data.
  final dynamic jsonData;

  /// List of available country codes to choose from.
  final List<String>? availableCountryCodes;

  /// Whether the input is required.
  final bool isRequired;

  /// The maximum length of the mobile number (excluding country code).
  final int maxLength;

  /// Whether to auto-format the number (e.g., (*************).
  final bool autoFormat;

  /// The format pattern to use if autoFormat is true.
  final String? formatPattern;

  /// Whether to show a clear button.
  final bool showClearButton;

  /// Whether the input is read-only.
  final bool readOnly;

  /// Whether the input is disabled.
  final bool isDisabled;

  /// The hint text to show when the input is empty.
  final String? hintText;

  /// The label text to show above the input.
  final String? labelText;

  /// The helper text to show below the input.
  final String? helperText;

  /// The error text to show when validation fails.
  final String? errorText;

  /// The text style of the input.
  final TextStyle? textStyle;

  /// The text style of the label.
  final TextStyle? labelStyle;

  /// The text style of the hint.
  final TextStyle? hintStyle;

  /// The text style of the helper text.
  final TextStyle? helperStyle;

  /// The text style of the error text.
  final TextStyle? errorStyle;

  /// The text style of the country code.
  final TextStyle? countryCodeStyle;

  /// The color of the input text.
  final Color? textColor;

  /// The color of the label text.
  final Color? labelColor;

  /// The color of the hint text.
  final Color? hintColor;

  /// The color of the helper text.
  final Color? helperColor;

  /// The color of the error text.
  final Color? errorColor;

  /// The color of the country code text.
  final Color? countryCodeColor;

  /// The color of the cursor.
  final Color? cursorColor;

  /// The color of the input background.
  final Color? backgroundColor;

  /// The color of the border.
  final Color? borderColor;

  /// The color of the focused border.
  final Color? focusedBorderColor;

  /// The color of the error border.
  final Color? errorBorderColor;

  /// The color of the clear button icon.
  final Color? clearButtonColor;

  /// The width of the border.
  final double borderWidth;

  /// The width of the focused border.
  final double focusedBorderWidth;

  /// The width of the error border.
  final double errorBorderWidth;

  /// The radius of the border corners.
  final double borderRadius;

  /// The width of the input.
  final double? width;

  /// The height of the input.
  final double? height;

  /// The padding around the input.
  final EdgeInsetsGeometry padding;

  /// The margin around the input.
  final EdgeInsetsGeometry margin;

  /// The prefix icon to show before the input.
  final IconData? prefixIcon;

  /// The suffix icon to show after the input.
  final IconData? suffixIcon;

  /// The color of the prefix icon.
  final Color? prefixIconColor;

  /// The color of the suffix icon.
  final Color? suffixIconColor;

  /// The size of the prefix icon.
  final double prefixIconSize;

  /// The size of the suffix icon.
  final double suffixIconSize;

  /// Whether to show a shadow under the input.
  final bool hasShadow;

  /// Whether to show a border around the input.
  final bool hasBorder;

  /// The elevation of the shadow.
  final double elevation;

  /// The color of the shadow.
  final Color? shadowColor;

  /// The keyboard type to use for the input.
  final TextInputType keyboardType;

  /// The text input action to use for the input.
  final TextInputAction textInputAction;

  /// The callback to execute when the input value changes.
  final void Function(String value)? onChanged;

  /// The callback to execute when the input is submitted.
  final void Function(String value)? onSubmitted;

  /// The callback to execute when the input is validated.
  final bool Function(String value)? validator;

  /// The callback to execute when the country code is changed.
  final void Function(String countryCode)? onCountryCodeChanged;

  /// The callback to execute when the clear button is pressed.
  final VoidCallback? onClear;

  /// The callback to execute when the input is tapped.
  final VoidCallback? onTap;

  /// Whether to auto-validate the input.
  final bool autoValidate;

  /// Whether to show a flag icon next to the country code.
  final bool showFlag;

  /// The map of country codes to flag asset paths.
  final Map<String, String>? flagAssets;

  /// The size of the flag icon.
  final double flagSize;

  /// Whether to show a dropdown for country code selection.
  final bool showCountryCodeDropdown;

  /// Whether to enable search functionality in the country code dropdown.
  final bool enableCountryCodeSearch;

  /// Whether to show the country name next to the country code.
  final bool showCountryName;

  /// The map of country codes to country names.
  final Map<String, String>? countryNames;

  /// Whether to show a copy button.
  final bool showCopyButton;

  /// The color of the copy button icon.
  final Color? copyButtonColor;

  /// The callback to execute when the copy button is pressed.
  final VoidCallback? onCopy;

  /// Whether to show a paste button.
  final bool showPasteButton;

  /// The color of the paste button icon.
  final Color? pasteButtonColor;

  /// The callback to execute when the paste button is pressed.
  final VoidCallback? onPaste;

  /// Called when the user hovers over the widget with a mouse.
  final void Function(bool)? onHover;

  /// Called when the widget gains or loses focus.
  final void Function(bool)? onFocusChange;

  /// Focus node for controlling the focus state of the widget.
  final FocusNode? focusNode;

  /// Whether the widget should be focused initially.
  final bool autofocus;

  /// Called when the user double taps on the widget.
  final GestureTapCallback? onDoubleTap;

  /// Called when the user long presses on the widget.
  final GestureLongPressCallback? onLongPress;

  /// Whether to provide haptic and/or acoustic feedback when tapped.
  final bool enableFeedback;

  /// Creates a mobile number widget.
  const MobileNumberWidget({
    super.key,
    this.initialValue,
    this.countryCode = '+91',
    this.showCountryCode = true,
    this.allowCountryCodeChange = true,
    this.availableCountryCodes,
    this.isRequired = true,
    this.maxLength = 10,
    this.autoFormat = false,
    this.formatPattern,
    this.showClearButton = true,
    this.readOnly = false,
    this.isDisabled = false,
    this.hintText = 'Phone Number',
    this.labelText,
    this.helperText,
    this.errorText,
    this.textStyle,
    this.labelStyle,
    this.hintStyle,
    this.helperStyle,
    this.errorStyle,
    this.countryCodeStyle,
    //this.textColor = const Color.fromARGB(255, 117, 117, 117),
    this.textColor = const Color(0xFF333333),
    this.labelColor,
    this.hintColor,
    this.helperColor,
    this.errorColor,
    this.countryCodeColor,
    this.cursorColor,
    this.backgroundColor = Colors.white,
    this.borderColor = const Color(0xFFCCCCCC),
    this.focusedBorderColor = const Color(0xFF0058FF),
    this.errorBorderColor,
    this.clearButtonColor,
    this.borderWidth = 1.0,
    this.focusedBorderWidth = 1.0,
    this.errorBorderWidth = 1.0,
    this.borderRadius = 4.0,
    this.width,
    this.height,
    //this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.padding = const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
    this.margin = EdgeInsets.zero,
    this.prefixIcon = Icons.phone,
    this.suffixIcon,
    this.prefixIconColor,
    this.suffixIconColor,
    this.prefixIconSize = 20.0,
    this.suffixIconSize = 20.0,
    this.hasShadow = false,
    this.hasBorder = true,
    this.elevation = 0.0,
    this.shadowColor,
    this.keyboardType = TextInputType.phone,
    this.textInputAction = TextInputAction.done,
    this.onChanged,
    this.onSubmitted,
    this.validator,
    this.onCountryCodeChanged,
    this.onClear,
    this.onTap,
    this.autoValidate = true,
    this.showFlag = false,
    this.flagAssets,
    this.flagSize = 24.0,
    this.showCountryCodeDropdown = true,
    this.enableCountryCodeSearch = true,
    this.showCountryName = false,
    this.countryNames,
    this.showCopyButton = false,
    this.copyButtonColor,
    this.onCopy,
    this.showPasteButton = false,
    this.pasteButtonColor,
    this.onPaste,
    this.jsonData,
    this.onHover,
    this.onFocusChange,
    this.focusNode,
    this.autofocus = false,
    this.onDoubleTap,
    this.onLongPress,
    this.enableFeedback = true,
  });

  /// Creates a MobileNumberWidget from a JSON map
  ///
  /// This factory constructor allows for full configuration of the MobileNumberWidget
  /// through a JSON object, making it easy to create widgets from API responses
  /// or configuration files.
  ///
  /// Example JSON:
  /// ```json
  /// {
  ///   "initialValue": "1234567890",
  ///   "countryCode": "+1",
  ///   "showCountryCode": true,
  ///   "labelText": "Phone Number",
  ///   "backgroundColor": "blue",
  ///   "textColor": "white"
  /// }
  /// ```
  factory MobileNumberWidget.fromJson(Map<String, dynamic> json) {
    // Parse colors
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;
      if (colorValue is Color) return colorValue;

      if (colorValue is String) {
        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          default:
            // Try to parse hex color
            if (colorValue.startsWith('#')) {
              final hex = colorValue.replaceFirst('#', '');
              if (hex.length == 6) {
                return Color(int.parse('0xFF$hex'));
              }
            }
        }
      }
      return null;
    }

    // Parse icon data
    IconData? parseIconData(String? iconName) {
      if (iconName == null) return null;

      switch (iconName.toLowerCase()) {
        case 'phone':
          return Icons.phone;
        case 'smartphone':
          return Icons.smartphone;
        case 'contact_phone':
          return Icons.contact_phone;
        case 'call':
          return Icons.call;
        case 'phone_android':
          return Icons.phone_android;
        case 'phone_iphone':
          return Icons.phone_iphone;
        case 'check':
          return Icons.check;
        case 'check_circle':
          return Icons.check_circle;
        case 'edit':
          return Icons.edit;
        case 'info':
          return Icons.info;
        case 'clear':
          return Icons.clear;
        case 'copy':
          return Icons.copy;
        case 'paste':
          return Icons.paste;
        default:
          return null;
      }
    }

    // Parse edge insets
    EdgeInsetsGeometry parseEdgeInsets(dynamic value) {
      if (value == null) {
        return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
      }

      if (value is EdgeInsetsGeometry) return value;

      if (value is double) {
        return EdgeInsets.all(value);
      }

      if (value is Map) {
        final left = (value['left'] as num?)?.toDouble() ?? 0.0;
        final top = (value['top'] as num?)?.toDouble() ?? 0.0;
        final right = (value['right'] as num?)?.toDouble() ?? 0.0;
        final bottom = (value['bottom'] as num?)?.toDouble() ?? 0.0;

        return EdgeInsets.fromLTRB(left, top, right, bottom);
      }

      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
    }

    // Parse text style
    TextStyle? parseTextStyle(Map<String, dynamic>? styleJson) {
      if (styleJson == null) return null;

      return TextStyle(
        color: parseColor(styleJson['color']),
        fontSize:
            styleJson['fontSize'] != null
                ? (styleJson['fontSize'] as num).toDouble()
                : null,
        fontWeight:
            styleJson['bold'] == true
                ? FontWeight.bold
                : styleJson['fontWeight'] != null
                ? FontWeight.values[styleJson['fontWeight'] as int]
                : null,
        fontStyle: styleJson['italic'] == true ? FontStyle.italic : null,
        decoration:
            styleJson['underline'] == true
                ? TextDecoration.underline
                : styleJson['lineThrough'] == true
                ? TextDecoration.lineThrough
                : null,
      );
    }

    return MobileNumberWidget(
      initialValue: json['initialValue'] as String?,
      countryCode: json['countryCode'] as String? ?? '+1',
      showCountryCode: json['showCountryCode'] as bool? ?? true,
      allowCountryCodeChange: json['allowCountryCodeChange'] as bool? ?? false,
      availableCountryCodes:
          json['availableCountryCodes'] != null
              ? List<String>.from(json['availableCountryCodes'] as List)
              : null,
      isRequired: json['isRequired'] as bool? ?? true,
      maxLength: json['maxLength'] as int? ?? 10,
      autoFormat: json['autoFormat'] as bool? ?? false,
      formatPattern: json['formatPattern'] as String?,
      showClearButton: json['showClearButton'] as bool? ?? true,
      readOnly: json['readOnly'] as bool? ?? false,
      isDisabled: json['isDisabled'] as bool? ?? false,
      hintText: json['hintText'] as String? ?? 'Enter mobile number',
      labelText: json['labelText'] as String?,
      helperText: json['helperText'] as String?,
      errorText: json['errorText'] as String?,
      textStyle: parseTextStyle(json['textStyle'] as Map<String, dynamic>?),
      labelStyle: parseTextStyle(json['labelStyle'] as Map<String, dynamic>?),
      hintStyle: parseTextStyle(json['hintStyle'] as Map<String, dynamic>?),
      helperStyle: parseTextStyle(json['helperStyle'] as Map<String, dynamic>?),
      errorStyle: parseTextStyle(json['errorStyle'] as Map<String, dynamic>?),
      countryCodeStyle: parseTextStyle(
        json['countryCodeStyle'] as Map<String, dynamic>?,
      ),
      textColor: parseColor(json['textColor']),
      labelColor: parseColor(json['labelColor']),
      hintColor: parseColor(json['hintColor']),
      helperColor: parseColor(json['helperColor']),
      errorColor: parseColor(json['errorColor']),
      countryCodeColor: parseColor(json['countryCodeColor']),
      cursorColor: parseColor(json['cursorColor']),
      backgroundColor: parseColor(json['backgroundColor']),
      borderColor: parseColor(json['borderColor']),
      focusedBorderColor: parseColor(json['focusedBorderColor']),
      errorBorderColor: parseColor(json['errorBorderColor']),
      clearButtonColor: parseColor(json['clearButtonColor']),
      borderWidth:
          json['borderWidth'] != null
              ? (json['borderWidth'] as num).toDouble()
              : 1.0,
      focusedBorderWidth:
          json['focusedBorderWidth'] != null
              ? (json['focusedBorderWidth'] as num).toDouble()
              : 2.0,
      errorBorderWidth:
          json['errorBorderWidth'] != null
              ? (json['errorBorderWidth'] as num).toDouble()
              : 1.0,
      borderRadius:
          json['borderRadius'] != null
              ? (json['borderRadius'] as num).toDouble()
              : 4.0,
      width: json['width'] != null ? (json['width'] as num).toDouble() : null,
      height:
          json['height'] != null ? (json['height'] as num).toDouble() : null,
      padding:
          json['padding'] != null
              ? parseEdgeInsets(json['padding'])
              : const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
      margin:
          json['margin'] != null
              ? parseEdgeInsets(json['margin'])
              : EdgeInsets.zero,
      prefixIcon: parseIconData(json['prefixIcon'] as String?),
      suffixIcon: parseIconData(json['suffixIcon'] as String?),
      prefixIconColor: parseColor(json['prefixIconColor']),
      suffixIconColor: parseColor(json['suffixIconColor']),
      prefixIconSize:
          json['prefixIconSize'] != null
              ? (json['prefixIconSize'] as num).toDouble()
              : 20.0,
      suffixIconSize:
          json['suffixIconSize'] != null
              ? (json['suffixIconSize'] as num).toDouble()
              : 20.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation:
          json['elevation'] != null
              ? (json['elevation'] as num).toDouble()
              : 2.0,
      shadowColor: parseColor(json['shadowColor']),
      keyboardType: TextInputType.phone,
      textInputAction: TextInputAction.done,
      autoValidate: json['autoValidate'] as bool? ?? true,
      showFlag: json['showFlag'] as bool? ?? false,
      flagSize:
          json['flagSize'] != null
              ? (json['flagSize'] as num).toDouble()
              : 24.0,
      showCountryCodeDropdown:
          json['showCountryCodeDropdown'] as bool? ?? false,
      showCountryName: json['showCountryName'] as bool? ?? false,
      showCopyButton: json['showCopyButton'] as bool? ?? false,
      copyButtonColor: parseColor(json['copyButtonColor']),
      showPasteButton: json['showPasteButton'] as bool? ?? false,
      pasteButtonColor: parseColor(json['pasteButtonColor']),
      jsonData: json['jsonData'],
      autofocus: json['autofocus'] as bool? ?? false,
      enableFeedback: json['enableFeedback'] as bool? ?? true,
    );
  }

  /// Converts the MobileNumberWidget to a JSON map
  ///
  /// This method allows for serializing the widget configuration to JSON,
  /// which can be useful for saving configurations or sharing them.
  Map<String, dynamic> toJson() {
    // Helper function to convert Color to String
    String? colorToString(Color? color) {
      if (color == null) return null;

      if (color == Colors.red) return 'red';
      if (color == Colors.blue) return 'blue';
      if (color == Colors.green) return 'green';
      if (color == Colors.yellow) return 'yellow';
      if (color == Colors.orange) return 'orange';
      if (color == Colors.purple) return 'purple';
      if (color == Colors.pink) return 'pink';
      if (color == Colors.brown) return 'brown';
      if (color == Colors.grey) return 'grey';
      if (color == Colors.black) return 'black';
      if (color == Colors.white) return 'white';

      // Convert to hex using ARGB components
      final int r = color.r.toInt();
      final int g = color.g.toInt();
      final int b = color.b.toInt();
      return '#${r.toRadixString(16).padLeft(2, '0')}${g.toRadixString(16).padLeft(2, '0')}${b.toRadixString(16).padLeft(2, '0')}';
    }

    // Helper function to convert IconData to String
    String? iconDataToString(IconData? icon) {
      if (icon == null) return null;

      if (icon == Icons.phone) return 'phone';
      if (icon == Icons.smartphone) return 'smartphone';
      if (icon == Icons.contact_phone) return 'contact_phone';
      if (icon == Icons.call) return 'call';
      if (icon == Icons.phone_android) return 'phone_android';
      if (icon == Icons.phone_iphone) return 'phone_iphone';
      if (icon == Icons.check) return 'check';
      if (icon == Icons.check_circle) return 'check_circle';
      if (icon == Icons.edit) return 'edit';
      if (icon == Icons.info) return 'info';
      if (icon == Icons.clear) return 'clear';
      if (icon == Icons.copy) return 'copy';
      if (icon == Icons.paste) return 'paste';

      return null;
    }

    // Helper function to convert EdgeInsetsGeometry to Map
    Map<String, dynamic>? edgeInsetsToMap(EdgeInsetsGeometry? insets) {
      if (insets == null) return null;

      if (insets is EdgeInsets) {
        return {
          'left': insets.left,
          'top': insets.top,
          'right': insets.right,
          'bottom': insets.bottom,
        };
      }

      return null;
    }

    // Helper function to convert TextStyle to Map
    Map<String, dynamic>? textStyleToMap(TextStyle? style) {
      if (style == null) return null;

      return {
        'color': colorToString(style.color),
        'fontSize': style.fontSize,
        'bold': style.fontWeight == FontWeight.bold,
        'italic': style.fontStyle == FontStyle.italic,
        'underline': style.decoration == TextDecoration.underline,
        'lineThrough': style.decoration == TextDecoration.lineThrough,
      };
    }

    return {
      'initialValue': initialValue,
      'countryCode': countryCode,
      'showCountryCode': showCountryCode,
      'allowCountryCodeChange': allowCountryCodeChange,
      'availableCountryCodes': availableCountryCodes,
      'isRequired': isRequired,
      'maxLength': maxLength,
      'autoFormat': autoFormat,
      'formatPattern': formatPattern,
      'showClearButton': showClearButton,
      'readOnly': readOnly,
      'isDisabled': isDisabled,
      'hintText': hintText,
      'labelText': labelText,
      'helperText': helperText,
      'errorText': errorText,
      'textStyle': textStyleToMap(textStyle),
      'labelStyle': textStyleToMap(labelStyle),
      'hintStyle': textStyleToMap(hintStyle),
      'helperStyle': textStyleToMap(helperStyle),
      'errorStyle': textStyleToMap(errorStyle),
      'countryCodeStyle': textStyleToMap(countryCodeStyle),
      'textColor': colorToString(textColor),
      'labelColor': colorToString(labelColor),
      'hintColor': colorToString(hintColor),
      'helperColor': colorToString(helperColor),
      'errorColor': colorToString(errorColor),
      'countryCodeColor': colorToString(countryCodeColor),
      'cursorColor': colorToString(cursorColor),
      'backgroundColor': colorToString(backgroundColor),
      'borderColor': colorToString(borderColor),
      'focusedBorderColor': colorToString(focusedBorderColor),
      'errorBorderColor': colorToString(errorBorderColor),
      'clearButtonColor': colorToString(clearButtonColor),
      'borderWidth': borderWidth,
      'focusedBorderWidth': focusedBorderWidth,
      'errorBorderWidth': errorBorderWidth,
      'borderRadius': borderRadius,
      'width': width,
      'height': height,
      'padding': edgeInsetsToMap(padding),
      'margin': edgeInsetsToMap(margin),
      'prefixIcon': iconDataToString(prefixIcon),
      'suffixIcon': iconDataToString(suffixIcon),
      'prefixIconColor': colorToString(prefixIconColor),
      'suffixIconColor': colorToString(suffixIconColor),
      'prefixIconSize': prefixIconSize,
      'suffixIconSize': suffixIconSize,
      'hasShadow': hasShadow,
      'elevation': elevation,
      'shadowColor': colorToString(shadowColor),
      'autoValidate': autoValidate,
      'showFlag': showFlag,
      'flagSize': flagSize,
      'showCountryCodeDropdown': showCountryCodeDropdown,
      'showCountryName': showCountryName,
      'showCopyButton': showCopyButton,
      'copyButtonColor': colorToString(copyButtonColor),
      'showPasteButton': showPasteButton,
      'pasteButtonColor': colorToString(pasteButtonColor),
      'jsonData': jsonData,
      'autofocus': autofocus,
      'enableFeedback': enableFeedback,
    };
  }

  @override
  MobileNumberWidgetState createState() => MobileNumberWidgetState();
}

class MobileNumberWidgetState extends State<MobileNumberWidget> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late TextEditingController _searchController;
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
    _searchController = TextEditingController();

    // Initialize provider after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<MobileNumberProvider>(
        context,
        listen: false,
      );
      provider.initialize(
        initialValue: widget.initialValue,
        countryCode: widget.countryCode,
        availableCountryCodes: widget.availableCountryCodes,
      );

      // Set controller text from provider
      _controller.text = provider.value;

      // Validate initial value if auto-validate is enabled
      if (widget.autoValidate && provider.value.isNotEmpty) {
        provider.validate(
          isRequired: widget.isRequired,
          customValidator: widget.validator,
          customErrorText: widget.errorText,
        );
      }

      // Set autofocus if specified
      if (widget.autofocus) {
        FocusScope.of(context).requestFocus(_focusNode);
      }
    });
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    _focusNode.dispose();
    _controller.dispose();
    _searchController.dispose();
    _closeDropdown();
    super.dispose();
  }

  void _onFocusChange() {
    final provider = Provider.of<MobileNumberProvider>(context, listen: false);
    provider.updateFocus(_focusNode.hasFocus);

    if (_focusNode.hasFocus && widget.onTap != null) {
      widget.onTap!();
    }

    // Call the onFocusChange callback if provided
    if (widget.onFocusChange != null) {
      widget.onFocusChange!(_focusNode.hasFocus);
    }
  }

  void _handleClear() {
    final provider = Provider.of<MobileNumberProvider>(context, listen: false);
    provider.clearValue();
    _controller.clear();

    if (widget.onClear != null) {
      widget.onClear!();
    }

    if (widget.onChanged != null) {
      widget.onChanged!('');
    }
  }

  void _handleCountryCodeChanged(String countryCode) {
    final provider = Provider.of<MobileNumberProvider>(context, listen: false);
    provider.updateCountryCode(countryCode);

    if (widget.onCountryCodeChanged != null) {
      widget.onCountryCodeChanged!(countryCode);
    }
  }

  Future<void> _handlePaste() async {
    final provider = Provider.of<MobileNumberProvider>(context, listen: false);
    await provider.handlePaste();
    _controller.text = provider.value;

    if (widget.onChanged != null) {
      widget.onChanged!(provider.value);
    }

    if (widget.onPaste != null) {
      widget.onPaste!();
    }
  }

  void _handleCopy() {
    final provider = Provider.of<MobileNumberProvider>(context, listen: false);
    provider.handleCopy();

    if (widget.onCopy != null) {
      widget.onCopy!();
    }

    // Show a snackbar to indicate the number was copied
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Mobile number copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  // Search functionality methods
  void _filterCountryCodes(String query) {
    final provider = Provider.of<MobileNumberProvider>(context, listen: false);
    provider.filterCountryCodes(query, widget.availableCountryCodes);
  }

  void _openDropdown() {
    final provider = Provider.of<MobileNumberProvider>(context, listen: false);
    if (provider.isDropdownOpen) return;

    provider.updateDropdownState(true);
    provider.filterCountryCodes('', widget.availableCountryCodes);
    _searchController.clear();

    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);
  }

  void _closeDropdown() {
    final provider = Provider.of<MobileNumberProvider>(context, listen: false);
    if (!provider.isDropdownOpen) return;

    provider.updateDropdownState(false);
    _overlayEntry?.remove();
    _overlayEntry = null;
    _searchController.clear();
  }

  OverlayEntry _createOverlayEntry() {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;
    final offset = renderBox.localToGlobal(Offset.zero);

    // Get the current provider instance to pass to the overlay
    final currentProvider = Provider.of<MobileNumberProvider>(
      context,
      listen: false,
    );

    return OverlayEntry(
      builder:
          (context) => ChangeNotifierProvider<MobileNumberProvider>.value(
            value: currentProvider,
            child: GestureDetector(
              onTap: _closeDropdown,
              behavior: HitTestBehavior.translucent,
              child: Stack(
                children: [
                  // Invisible overlay to capture taps outside
                  Positioned.fill(child: Container(color: Colors.transparent)),
                  // Dropdown positioned relative to the input field
                  Positioned(
                    left: offset.dx,
                    top: offset.dy, // Align with input field
                    width: 110,
                    //size.width,
                    child: Material(
                      elevation: 8,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(widget.borderRadius),
                        topRight: Radius.circular(widget.borderRadius),
                        bottomLeft: Radius.circular(widget.borderRadius),
                        bottomRight: Radius.circular(widget.borderRadius),
                      ),
                      child: Container(
                        constraints: const BoxConstraints(maxHeight: 250),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(widget.borderRadius),
                            topRight: Radius.circular(widget.borderRadius),
                            bottomLeft: Radius.circular(widget.borderRadius),
                            bottomRight: Radius.circular(widget.borderRadius),
                          ),

                          // border: Border.all(
                          //   color:
                          //       widget.focusedBorderColor ??
                          //       const Color(0xFF0058FF),
                          //   width: widget.borderWidth,
                          // ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(
                                0.1,
                              ), // Soft shadow
                              blurRadius: 8, // Spread of the shadow
                              offset: Offset(0, 2), // Vertical shadow
                            ),
                          ],
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Search field
                            if (widget.enableCountryCodeSearch) ...[
                              Container(
                                //padding: const EdgeInsets.all(12.0),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade50,
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(
                                      widget.borderRadius - 1,
                                    ),
                                    topRight: Radius.circular(
                                      widget.borderRadius - 1,
                                    ),
                                  ),
                                ),
                                child: TextField(
                                  controller: _searchController,

                                  autofocus: true,
                                  // style: TextStyle(
                                  //   fontSize: _getResponsiveValueFontSize(
                                  //     context,
                                  //   ),
                                  //   fontFamily: 'Inter',
                                  // ),
                                  style: FontManager.getCustomStyle(
                                    fontFamily: FontManager.fontFamilyInter,
                                    fontWeight: FontManager.medium,
                                    fontSize: _getResponsiveValueFontSize(
                                      context,
                                    ),
                                  ),
                                  decoration: InputDecoration(
                                    hintText: 'Search country...',
                                    // hintStyle: TextStyle(
                                    //   fontSize: _getResponsiveValueFontSize(
                                    //     context,
                                    //   ),
                                    //   fontFamily: 'Inter',
                                    //   color: Colors.grey.shade400,
                                    // ),
                                    hintStyle: FontManager.getCustomStyle(
                                      fontFamily: FontManager.fontFamilyInter,
                                      fontWeight: FontManager.medium,
                                      fontSize: _getResponsiveValueFontSize(
                                        context,
                                      ),
                                      color: Colors.grey.shade400,
                                    ),
                                    prefixIcon: Icon(
                                      Icons.search,
                                      size: _getResponsiveIconSize(context),
                                      color: Colors.grey.shade600,
                                    ),
                                    suffixIcon:
                                        _searchController.text.isNotEmpty
                                            ? IconButton(
                                              icon: Icon(
                                                Icons.clear,
                                                size: _getResponsiveIconSize(
                                                  context,
                                                ),
                                                color: Colors.grey.shade600,
                                              ),
                                              onPressed: () {
                                                _searchController.clear();
                                                _filterCountryCodes('');
                                              },
                                            )
                                            : null,
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(4),
                                      borderSide: BorderSide.none,
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(4),
                                      borderSide: BorderSide.none,
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(4),
                                      borderSide: BorderSide.none,
                                    ),
                                    // contentPadding: const EdgeInsets.symmetric(
                                    //   horizontal: 12,
                                    //   vertical: 8,
                                    // ),
                                    contentPadding: _getResponsivePadding(
                                      context,
                                    ),
                                    isDense: true,
                                    filled: true,
                                    fillColor: Colors.white,
                                  ),
                                  onChanged: _filterCountryCodes,
                                ),
                              ),
                            ],
                            // Country codes list
                            Consumer<MobileNumberProvider>(
                              builder:
                                  (context, provider, child) => Flexible(
                                    child:
                                        provider.filteredCountryCodes.isEmpty
                                            ? Padding(
                                              padding: const EdgeInsets.all(
                                                16.0,
                                              ),
                                              child: Text(
                                                'No countries found',
                                                // style: TextStyle(
                                                //   color: Colors.grey,
                                                //   fontSize:
                                                //       _getResponsiveValueFontSize(
                                                //         context,
                                                //       ),
                                                //   fontFamily: 'Inter',
                                                // ),
                                                style: FontManager.getCustomStyle(
                                                  fontFamily:
                                                      FontManager
                                                          .fontFamilyInter,
                                                  fontWeight:
                                                      FontManager.medium,
                                                  fontSize:
                                                      _getResponsiveValueFontSize(
                                                        context,
                                                      ),
                                                  color: Colors.grey,
                                                ),
                                              ),
                                            )
                                            : ListView.builder(
                                              shrinkWrap: true,
                                              padding: EdgeInsets.zero,
                                              itemCount:
                                                  provider
                                                      .filteredCountryCodes
                                                      .length,
                                              itemBuilder: (context, index) {
                                                final code =
                                                    provider
                                                        .filteredCountryCodes[index];
                                                final isSelected =
                                                    code ==
                                                    provider
                                                        .selectedCountryCode;

                                                return InkWell(
                                                  onTap: () {
                                                    _handleCountryCodeChanged(
                                                      code,
                                                    );
                                                    _closeDropdown();
                                                  },
                                                  child: Container(
                                                    padding:
                                                        const EdgeInsets.symmetric(
                                                          horizontal: 16,
                                                          vertical: 8,
                                                        ),
                                                    decoration: BoxDecoration(
                                                      color:
                                                          isSelected
                                                              ? (widget.focusedBorderColor ??
                                                                      const Color(
                                                                        0xFF0058FF,
                                                                      ))
                                                                  .withOpacity(
                                                                    0.1,
                                                                  )
                                                              : Colors
                                                                  .transparent,
                                                    ),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .center,
                                                      children: [
                                                        Text(
                                                          code,
                                                          // style: TextStyle(
                                                          //   fontWeight:
                                                          //       FontWeight
                                                          //           .normal,
                                                          //   fontSize:
                                                          //       _getResponsiveValueFontSize(
                                                          //         context,
                                                          //       ),
                                                          //   fontFamily: 'Inter',
                                                          //   color:
                                                          //       isSelected
                                                          //           ? (widget
                                                          //                   .focusedBorderColor ??
                                                          //               const Color(
                                                          //                 0xFF0058FF,
                                                          //               ))
                                                          //           : Colors
                                                          //               .black,
                                                          // ),
                                                          style: FontManager.getCustomStyle(
                                                            fontFamily:
                                                                FontManager
                                                                    .fontFamilyInter,
                                                            fontWeight:
                                                                FontManager
                                                                    .medium,
                                                            fontSize:
                                                                _getResponsiveValueFontSize(
                                                                  context,
                                                                ),
                                                            color:
                                                                isSelected
                                                                    ? (widget
                                                                            .focusedBorderColor ??
                                                                        const Color(
                                                                          0xFF0058FF,
                                                                        ))
                                                                    : Color(
                                                                      0xFF333333,
                                                                    ),
                                                          ),
                                                        ),
                                                        if (widget
                                                            .showCountryName) ...[
                                                          const SizedBox(
                                                            width: 12,
                                                          ),
                                                          Expanded(
                                                            child: Text(
                                                              _getCountryName(
                                                                code,
                                                                provider,
                                                              ),
                                                              // style: TextStyle(
                                                              //   fontSize:
                                                              //       _getResponsiveValueFontSize(
                                                              //         context,
                                                              //       ) -
                                                              //       2,
                                                              //   fontFamily:
                                                              //       'Inter',
                                                              //   color:
                                                              //       Colors
                                                              //           .grey
                                                              //           .shade600,
                                                              // ),
                                                              style: FontManager.getCustomStyle(
                                                                fontFamily:
                                                                    FontManager
                                                                        .fontFamilyInter,
                                                                fontWeight:
                                                                    FontManager
                                                                        .medium,
                                                                fontSize:
                                                                    _getResponsiveValueFontSize(
                                                                      context,
                                                                    ) -
                                                                    2,
                                                                color:
                                                                    Colors.grey,
                                                              ),
                                                              overflow:
                                                                  TextOverflow
                                                                      .ellipsis,
                                                            ),
                                                          ),
                                                        ],
                                                      ],
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                  ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  String _getFlag(String countryCode, MobileNumberProvider provider) {
    if (widget.flagAssets != null &&
        widget.flagAssets!.containsKey(countryCode)) {
      return widget.flagAssets![countryCode]!;
    }
    return provider.getFlag(countryCode);
  }

  String _getCountryName(String countryCode, MobileNumberProvider provider) {
    if (widget.countryNames != null &&
        widget.countryNames!.containsKey(countryCode)) {
      return widget.countryNames![countryCode]!;
    }
    return provider.getCountryName(countryCode);
  }

  @override
  Widget build(BuildContext context) {
    // If jsonData is provided, display it instead of the regular widget
    if (widget.jsonData != null) {
      return Container(
        width: widget.width,
        height: widget.height,
        margin: widget.margin,
        padding: widget.padding,
        decoration: BoxDecoration(
          color: widget.backgroundColor,
          borderRadius: BorderRadius.circular(widget.borderRadius),
          border: Border.all(
            color: widget.borderColor ?? const Color(0xFFCCCCCC),
            width: widget.borderWidth,
          ),
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.labelText != null) ...[
                Text(
                  widget.labelText!,
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
              ],
              Text(
                widget.jsonData is String
                    ? widget.jsonData as String
                    : widget.jsonData is Map || widget.jsonData is List
                    ? const JsonEncoder.withIndent(
                      '  ',
                    ).convert(widget.jsonData)
                    : widget.jsonData.toString(),
              ),
            ],
          ),
        ),
      );
    }

    return Consumer<MobileNumberProvider>(
      builder: (context, provider, child) {
        // Create the text field with responsive container wrapper
        Widget textField = MouseRegion(
          onEnter: (_) {
            provider.updateHover(true);
            if (widget.onHover != null) {
              widget.onHover!(true);
            }
          },
          onExit: (_) {
            provider.updateHover(false);
            if (widget.onHover != null) {
              widget.onHover!(false);
            }
          },
          child: Theme(
            data: Theme.of(context).copyWith(
              inputDecorationTheme: const InputDecorationTheme(
                hoverColor: Colors.transparent,
                focusColor: Colors.transparent,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  height: _getResponsiveHeight(context),
                  alignment: Alignment.center,
                  child: TextField(
                    controller: _controller,
                    focusNode: _focusNode,
                    keyboardType: widget.keyboardType,
                    textInputAction: widget.textInputAction,
                    // style: TextStyle(
                    //   color: widget.isDisabled ? Colors.grey : widget.textColor,
                    //   fontSize: _getResponsiveValueFontSize(context),
                    //   fontFamily: 'Inter',
                    //   fontWeight: FontWeight.normal,
                    //   //height: _getResponsiveLineHeightPhone(context),
                    //   height: 1.12,
                    // ),
                    style: FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontWeight: FontManager.medium,
                      color: widget.isDisabled ? Colors.grey : widget.textColor,
                      fontSize: _getResponsiveValueFontSize(context),
                      height: 1.1,
                    ),
                    decoration: InputDecoration(
                      constraints: BoxConstraints(
                        minHeight: _getResponsiveHeight(context),
                      ),
                      isDense: true,
                      hintText: widget.hintText,
                      // hintStyle: TextStyle(
                      //   fontSize: _getResponsiveValueFontSize(context),
                      //   fontFamily: 'Inter',
                      //   color: Colors.grey.shade400,
                      // ),
                      hintStyle: FontManager.getCustomStyle(
                        fontFamily: FontManager.fontFamilyInter,
                        fontWeight: FontManager.medium,
                        color: Colors.grey.shade400,
                        fontSize: _getResponsiveValueFontSize(context),
                      ),
                      filled: true,
                      fillColor:
                          widget.isDisabled
                              ? Colors.grey.shade200
                              : widget.backgroundColor,
                      prefix:
                          widget.showCountryCode
                              ? _buildSimpleCountryCodeSelector(provider)
                              : null,
                      suffixIcon: _buildSuffixIcon(provider),
                      border:
                          widget.hasBorder
                              ? OutlineInputBorder(
                                borderRadius: BorderRadius.circular(
                                  widget.borderRadius,
                                ),
                                borderSide: BorderSide(
                                  color:
                                      provider.hasError
                                          ? (widget.errorBorderColor ??
                                              Colors.red)
                                          : (widget.borderColor ??
                                              const Color(0xFFCCCCCC)),
                                  width: widget.borderWidth,
                                ),
                              )
                              : InputBorder.none,
                      enabledBorder:
                          widget.hasBorder
                              ? OutlineInputBorder(
                                borderRadius: BorderRadius.circular(
                                  widget.borderRadius,
                                ),
                                borderSide: BorderSide(
                                  color:
                                      provider.hasError
                                          ? (widget.errorBorderColor ??
                                              Colors.red)
                                          : provider.isHovering
                                          ? (widget.focusedBorderColor ??
                                              const Color(0xFF0058FF))
                                          : (widget.borderColor ??
                                              const Color(0xFFCCCCCC)),
                                  width: widget.borderWidth,
                                ),
                              )
                              : null,
                      focusedBorder:
                          widget.hasBorder
                              ? OutlineInputBorder(
                                borderRadius: BorderRadius.circular(
                                  widget.borderRadius,
                                ),
                                borderSide: BorderSide(
                                  color:
                                      provider.hasError
                                          ? (widget.errorBorderColor ??
                                              Colors.red)
                                          : (widget.focusedBorderColor ??
                                              const Color(0xFF0058FF)),
                                  width: widget.borderWidth,
                                ),
                              )
                              : null,
                      errorBorder:
                          widget.hasBorder
                              ? OutlineInputBorder(
                                borderRadius: BorderRadius.circular(
                                  widget.borderRadius,
                                ),
                              )
                              : null,
                    ),
                    enabled: !widget.isDisabled && !widget.readOnly,
                    readOnly: widget.readOnly,
                    cursorColor: widget.cursorColor,
                    onChanged: (value) {
                      // Update provider value
                      provider.updateValue(value);

                      // Trigger validation if auto-validate is enabled
                      if (widget.autoValidate) {
                        provider.validate(
                          isRequired: widget.isRequired,
                          customValidator: widget.validator,
                          customErrorText: widget.errorText,
                        );
                      }

                      // Call the user's onChanged callback if provided
                      if (widget.onChanged != null) {
                        widget.onChanged!(value);
                      }
                    },
                    onSubmitted: (value) {
                      final isValid = provider.validate(
                        isRequired: widget.isRequired,
                        customValidator: widget.validator,
                        customErrorText: widget.errorText,
                      );

                      if (widget.onSubmitted != null && isValid) {
                        widget.onSubmitted!(value);
                      }
                    },
                    onTap: () {
                      if (widget.onTap != null) {
                        widget.onTap!();
                      }
                    },
                  ),
                ),
                if (provider.hasError && provider.errorText != null)
                  Text(
                    provider.errorText!,
                    // style: TextStyle(
                    //   color: widget.errorColor ?? Colors.red,
                    //   fontSize: 10,
                    //   //_getResponsiveValueFontSize(context),
                    // ),
                    style: FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontWeight: FontManager.medium,
                      color: widget.errorColor ?? Colors.red,
                      fontSize: _getResponsiveValueFontSize(context),
                    ),
                  ),
              ],
            ),
          ),
        );

        // Never apply shadow - always return the clean textField
        return textField;
      },
    );
  }

  Widget _buildSimpleCountryCodeSelector(MobileNumberProvider provider) {
    if (!widget.showCountryCode) {
      return const SizedBox.shrink();
    }

    if (widget.showCountryCodeDropdown && widget.allowCountryCodeChange) {
      // Use searchable dropdown if search is enabled
      if (widget.enableCountryCodeSearch) {
        return CompositedTransformTarget(
          link: _layerLink,
          child: GestureDetector(
            onTap: widget.isDisabled || widget.readOnly ? null : _openDropdown,
            child: Container(
              padding: const EdgeInsets.only(right: 8.0),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    provider.selectedCountryCode,
                    // style: TextStyle(
                    //   //fontSize: _getResponsiveValueFontSize(context),
                    //   fontSize: (_getResponsiveValueFontSize(context) - 0.65)
                    //       .clamp(0.0, double.infinity),
                    //   fontFamily: 'Inter',
                    //   fontWeight: FontWeight.normal,
                    //   color: Colors.black.withOpacity(0.85),
                    // ),
                    style: FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontWeight: FontManager.medium,
                      color: Color(0xFF333333),
                      fontSize: _getResponsiveValueFontSize(context),
                    ),
                  ),

                  const SizedBox(width: 4),
                  Icon(
                    Icons.arrow_drop_down,
                    size: _getResponsiveIconSize(context),
                    color:
                        widget.isDisabled
                            ? const Color(0xFFCCCCCC)
                            : provider.isHovering
                            ? (widget.focusedBorderColor ??
                                const Color(0xFF0058FF))
                            : provider.hasFocus
                            ? (widget.focusedBorderColor ??
                                const Color(0xFF0058FF))
                            : const Color(0xFFCCCCCC),
                  ),
                ],
              ),
            ),
          ),
        );
      } else {
        // Use standard dropdown if search is disabled
        final List<String> countryCodes =
            widget.availableCountryCodes ?? provider.filteredCountryCodes;

        return Container(
          padding: const EdgeInsets.only(right: 8.0),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: provider.selectedCountryCode,
              isDense: true,
              icon: Icon(
                Icons.arrow_drop_down,
                size: _getResponsiveIconSize(context),
                color:
                    widget.isDisabled
                        ? Colors.grey.shade400
                        : provider.isHovering
                        ? (widget.focusedBorderColor ?? const Color(0xFF0058FF))
                        : provider.hasFocus
                        ? (widget.focusedBorderColor ?? const Color(0xFF0058FF))
                        : Colors.grey.shade600,
              ),
              onChanged:
                  widget.isDisabled || widget.readOnly
                      ? null
                      : (String? newValue) {
                        if (newValue != null) {
                          _handleCountryCodeChanged(newValue);
                        }
                      },
              items:
                  countryCodes.map<DropdownMenuItem<String>>((String code) {
                    return DropdownMenuItem<String>(
                      value: code,
                      child: Text(
                        code,
                        textAlign: TextAlign.center,
                        // style: TextStyle(
                        //   fontSize: _getResponsiveValueFontSize(context),
                        //   fontFamily: 'Inter',
                        //   fontWeight: FontWeight.normal,
                        //   color:
                        //       widget.isDisabled
                        //           ? Colors.grey.shade400
                        //           : provider.isHovering
                        //           ? widget.focusedBorderColor
                        //           : provider.hasFocus
                        //           ? widget.focusedBorderColor
                        //           : Colors.black,
                        // ),
                        style: FontManager.getCustomStyle(
                          fontFamily: FontManager.fontFamilyInter,
                          fontWeight: FontManager.medium,
                          color:
                              widget.isDisabled
                                  ? Colors.grey.shade400
                                  : provider.isHovering
                                  ? widget.focusedBorderColor
                                  : provider.hasFocus
                                  ? widget.focusedBorderColor
                                  : Colors.black,
                          fontSize: _getResponsiveValueFontSize(context),
                        ),
                      ),
                    );
                  }).toList(),
            ),
          ),
        );
      }
    } else {
      return Container(
        padding: const EdgeInsets.only(right: 8.0),
        child: Text(
          provider.selectedCountryCode,
          // style: TextStyle(
          //   fontSize: _getResponsiveValueFontSize(context),
          //   fontFamily: 'Inter',
          //   fontWeight: FontWeight.w600,
          //   color:
          //       widget.isDisabled
          //           ? Colors.grey.shade400
          //           : provider.isHovering
          //           ? widget.focusedBorderColor
          //           : provider.hasFocus
          //           ? widget.focusedBorderColor
          //           : Colors.grey.shade600,
          // ),
          style: FontManager.getCustomStyle(
            fontFamily: FontManager.fontFamilyInter,
            fontWeight: FontManager.medium,
            fontSize: _getResponsiveValueFontSize(context),
            color:
                widget.isDisabled
                    ? Colors.grey.shade400
                    : provider.isHovering
                    ? widget.focusedBorderColor
                    : provider.hasFocus
                    ? widget.focusedBorderColor
                    : Colors.grey.shade600,
          ),
        ),
      );
    }
  }

  Widget? _buildSuffixIcon(MobileNumberProvider provider) {
    return Transform.scale(
      scale: 0.6,
      child: Container(
        padding: const EdgeInsets.only(right: 0.0),
        // child: SvgPicture.asset(
        //   'assets/images/phone-icon.svg',
        //   package: 'ui_controls_library',
        //   //width: _getResponsiveIconSize(context),
        //   // colorFilter: ColorFilter.mode(
        //   //   widget.isDisabled
        //   //       ? Colors.grey.shade400
        //   //       : provider.isHovering
        //   //       ? (widget.focusedBorderColor ?? const Color(0xFF0058FF))
        //   //       : provider.hasFocus
        //   //       ? (widget.focusedBorderColor ?? const Color(0xFF0058FF))
        //   //       : Colors.grey.shade600,
        //   //   BlendMode.srcIn,
        //   // ),
        // ),
        child: SvgPicture.asset(
          provider.isHovering
              ? 'assets/images/phone-icon-hover.svg'
              : 'assets/images/phone-icon.svg',
          package: 'ui_controls_library',
          // width: _getResponsiveIconSize(context),
        ),
      ),
    );
  }

  int min(int a, int b) => a < b ? a : b;

  double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 14.0; // Default for very small screens
    }
  }
}

double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px) - Reduced from 56.0
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px) - Reduced from 48.0
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px) - Reduced from 40.0
  } else if (screenWidth >= 768) {
    return 40.0; // Small (768-1024px) - Reduced from 40.0
  } else {
    return 40.0; // Default for very small screens - Reduced from 40.0
  }
}

double _getResponsiveIconSize(BuildContext context) {
  final double screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 20.0; // Large
  } else if (screenWidth >= 1280) {
    return 22.0; // Medium
  } else if (screenWidth >= 768) {
    return 24.0; // Small
  } else {
    return 24.0; // Extra Small (fallback for very small screens)
  }
}

double _getResponsiveImageSize(BuildContext context) {
  final double screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth > 1920) {
    return 24.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 24.0; // Large
  } else if (screenWidth >= 1280) {
    return 20.0; // Medium
  } else if (screenWidth >= 768) {
    return 16.0; // Small
  } else {
    return 16.0; // Extra Small (fallback for very small screens)
  }
}
// double _getResponsiveLineHeightPhone(BuildContext context) {
//   final double screenWidth = MediaQuery.of(context).size.width.clamp(0, 1280);
//   if (screenWidth >= 1280) {
//     return 0.3;
//   } else if (screenWidth >= 768) {
//     return 0.3;
//   } else {
//     return 0.3;
//   }
// }

EdgeInsets _getResponsivePadding(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth >= 1440) {
    return const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 4.0,
    ); // Extra Large
  } else if (screenWidth >= 1280) {
    return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 3.0); // Large
  } else if (screenWidth >= 768) {
    return const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0); // Medium
  } else {
    return const EdgeInsets.symmetric(
      horizontal: 6.0,
      vertical: 1.0,
    ); // Default for very small screens
  }
}
