import 'package:flutter/material.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/screens/web/new_design/widgets/chat_widgets/accordion_controller.dart';
import 'package:nsl/screens/new_design/object_flow_static_mobile/all_smart_resolution_object_mobile.dart';
import 'package:provider/provider.dart';

class ExtractDetailsObjectMobile extends StatefulWidget {
  const ExtractDetailsObjectMobile({super.key});

  @override
  State<ExtractDetailsObjectMobile> createState() =>
      _ExtractDetailsObjectMobileState();
}

class _ExtractDetailsObjectMobileState
    extends State<ExtractDetailsObjectMobile> {
  late AccordionController _accordionController;

  // Track expanded state for accordion items
  Map<String, bool> expandedStates = {
    'Object Details': false,
    'Attributes Details': false,
    'Entity Relationship': false,
    'Attribute Business Rules': false,
    'Enumerated Values': false,
    'System Permissions': false,
    'Security Classification': false,
  };

  // Track expanded state for object items in AI mode
  Map<String, bool> objectExpandedStates = {
    'Object: Customer': false,
    'Object: Product': false,
    'Object: Order': false,
  };

  // Data storage for tables
  List<Map<String, String>> _attributeData = [
    {
      'attributeName': 'customer_id',
      'displayName': 'Customer ID',
      'dataType': 'string',
      'required': 'YES',
      'unique': 'YES'
    },
    {
      'attributeName': 'email',
      'displayName': 'Email Address',
      'dataType': 'string',
      'required': 'YES',
      'unique': 'YES'
    },
    {
      'attributeName': 'name',
      'displayName': 'Full Name',
      'dataType': 'string',
      'required': 'YES',
      'unique': 'NO'
    },
  ];

  @override
  void initState() {
    super.initState();
    _accordionController = AccordionController();
    _accordionController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _accordionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<WebHomeProviderStatic>(
      builder: (context, provider, child) {
        return Scaffold(
          backgroundColor: Colors.black,
          body: Column(
            children: [
              // Header with toggle
              _buildHeader(context, provider),

              // Content area with positioned bottom actions
              Expanded(
                child: Stack(
                  children: [
                    Container(
                      color: Colors.white,
                      child: _buildContent(context, provider),
                    ),
                    // Bottom action buttons positioned on top
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: _buildBottomActions(context),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context, WebHomeProviderStatic provider) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.black,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Dynamic header label based on toggle state
          Text(
            provider.isAIMode ? 'Extracted Details' : 'Objects',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.white,
              fontWeight: FontWeight.w600,
              height: 1,
            ),
          ),

          // AI/Manual Toggle
          _buildAIManualToggle(context, provider),
        ],
      ),
    );
  }

  Widget _buildAIManualToggle(
      BuildContext context, WebHomeProviderStatic provider) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          provider.isAIMode ? 'Form' : 'Manually Process',
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.titleSmall(context),
            color: Colors.white,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            height: 1,
          ),
        ),
        const SizedBox(width: 8),
        MouseRegion(
          cursor: SystemMouseCursors.click,
          child: GestureDetector(
            onTap: () {
              provider.toggleAIMode();
            },
            child: Container(
              width: 34,
              height: 18,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                  color: Colors.black,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(10),
              ),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                curve: Curves.easeInOut,
                alignment: provider.isAIMode
                    ? Alignment.centerLeft
                    : Alignment.centerRight,
                child: Container(
                  width: 16,
                  height: 16,
                  decoration: const BoxDecoration(
                    color: Color(0xFF0058FF),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContent(BuildContext context, WebHomeProviderStatic provider) {
    if (provider.isAIMode) {
      return _buildExtractedDetailsTab(context);
    } else {
      return _buildObjectsTab(context);
    }
  }

  Widget _buildExtractedDetailsTab(BuildContext context) {
    return Container(
      height: double.infinity,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section with "Manually Process"
            Text(
              'Manually Process',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w400,
                height: 1,
              ),
            ),
            const SizedBox(height: 16),

            // Object items matching the first design
            _buildObjectItem(context, 'Object: Customer'),
            const SizedBox(height: 12),
            _buildObjectItem(context, 'Object: Product'),
            const SizedBox(height: 12),
            _buildObjectItem(context, 'Object: Order'),
          ],
        ),
      ),
    );
  }

  Widget _buildObjectsTab(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Object header with search
          _buildObjectHeader(context),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildObjectItem(BuildContext context, String title) {
    bool isExpanded = objectExpandedStates[title] ?? false;

    return Container(
      margin: const EdgeInsets.only(bottom: 2),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 14),
            child: Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        // Close all other object accordions when opening one
                        objectExpandedStates.updateAll((key, value) => false);
                        objectExpandedStates[title] = !isExpanded;
                      });
                    },
                    child: Text(
                      title,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyLarge(context),
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                        fontWeight: FontWeight.w500,
                        height: 1.2,
                      ),
                    ),
                  ),
                ),
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () => _showNotificationPopup(context),
                    child: Container(
                      width: 20,
                      height: 20,
                      child: Icon(
                        Icons.notifications_outlined,
                        size: 24,
                        color: Colors.red,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (isExpanded)
            Container(
              padding: const EdgeInsets.fromLTRB(8, 0, 8, 16),
              child: Column(
                children: [
                  _buildNestedAccordionItem(
                    context,
                    'Object Details',
                    'Partial Completion',
                    '1 Entity Detected',
                    const Color(0xFFFEF3C7),
                    const Color(0xFF92400E),
                    true,
                  ),
                  _buildNestedAccordionItem(
                    context,
                    'Attributes Details',
                    'Completed',
                    '25 Attributes',
                    const Color(0xFFD1FAE5),
                    const Color(0xFF065F46),
                    true,
                  ),
                  _buildNestedAccordionItem(
                    context,
                    'Entity Relationships',
                    'Partial Completion',
                    '0 rules Configured',
                    const Color(0xFFFEF3C7),
                    const Color(0xFF92400E),
                    false,
                  ),
                  _buildNestedAccordionItem(
                    context,
                    'Attribute Business Rules',
                    'Missing',
                    '0 Configure',
                    const Color(0xFFFEE2E2),
                    const Color(0xFF991B1B),
                    false,
                  ),
                  _buildNestedAccordionItem(
                    context,
                    'Enumerated Values',
                    'Missing',
                    '0 Configure',
                    const Color(0xFFFEE2E2),
                    const Color(0xFF991B1B),
                    false,
                  ),
                  _buildNestedAccordionItem(
                    context,
                    'System Permissions',
                    'Not Configured',
                    '0 Configure',
                    const Color(0xFFFEE2E2),
                    const Color(0xFF991B1B),
                    false,
                  ),
                  _buildNestedAccordionItem(
                    context,
                    'Security Classification',
                    'Not Configured',
                    '0 Configure',
                    const Color(0xFFFEE2E2),
                    const Color(0xFF991B1B),
                    false,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNestedAccordionItem(
    BuildContext context,
    String title,
    String status,
    String count,
    Color backgroundColor,
    Color textColor,
    bool showAttributeTable,
  ) {
    final isExpanded = _accordionController.isPanelExpanded('nested_$title');

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(
          color: isExpanded ? const Color(0xFF0058FF) : const Color(0xFFE5E7EB),
          width: isExpanded ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              _accordionController.togglePanel('nested_$title');
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
              child: Row(
                children: [
                  // Title Text
                  Expanded(
                    child: Text(
                      title,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  // Status Badge and Count in a column on the right
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: backgroundColor,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          status,
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.labelSmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: textColor,
                          ),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        count,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.labelSmall(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded) ...[
            Container(
              decoration: const BoxDecoration(
                color: Colors.white,
              ),
              child: title == 'Attributes Details'
                  ? _buildMobileAttributeTable(context)
                  : _buildPlaceholderContent(context, title),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMobileAttributeTable(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and Add button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Attribute Configuration',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () => _showAddAttributeModal(context),
                icon: const Icon(Icons.add, size: 16),
                label: const Text('Add'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0058FF),
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  elevation: 0,
                  textStyle: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelSmall(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Mobile-friendly attribute list
          ..._attributeData.asMap().entries.map((entry) {
            int index = entry.key;
            Map<String, String> data = entry.value;
            return _buildMobileAttributeCard(context, index, data);
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildMobileAttributeCard(
      BuildContext context, int index, Map<String, String> data) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE5E7EB)),
        borderRadius: BorderRadius.circular(6),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Attribute name and actions row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  data['attributeName']!,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontWeight: FontWeight.w600,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                  ),
                ),
              ),
              Row(
                children: [
                  IconButton(
                    onPressed: () => _showEditAttributeModal(
                      context,
                      index,
                      data['attributeName']!,
                      data['displayName']!,
                      data['dataType']!,
                      data['required']!,
                      data['unique']!,
                    ),
                    icon: const Icon(Icons.edit_outlined),
                    color: Colors.blue[600],
                    iconSize: 18,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  IconButton(
                    onPressed: () => _deleteAttribute(
                        context, index, data['attributeName']!),
                    icon: const Icon(Icons.delete_outline),
                    color: Colors.red[600],
                    iconSize: 18,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          // Attribute details
          Text(
            'Display: ${data['displayName']}',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodySmall(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  data['dataType']!,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelSmall(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.blue[700],
                  ),
                ),
              ),
              const SizedBox(width: 8),
              if (data['required'] == 'YES')
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.orange[50],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'Required',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.orange[700],
                    ),
                  ),
                ),
              const SizedBox(width: 8),
              if (data['unique'] == 'YES')
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'Unique',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.labelSmall(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.green[700],
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderContent(BuildContext context, String title) {
    return Container(
      padding: const EdgeInsets.all(6),
      margin: EdgeInsets.symmetric(horizontal: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and Add button
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _getPlaceholderTitle(title),
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelSmall(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () => _showAddModal(context, title),
                icon: const Icon(Icons.add, size: 16),
                label: Text(
                  _getAddButtonText(title),
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.labelSmall(context),
                    fontWeight: FontWeight.w600,
                    fontFamily: FontManager.fontFamilyTiemposText,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0058FF),
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  alignment: Alignment.center, // Ensures vertical center
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Table container with internal scroll and fixed last column
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              children: [
                // Scrollable table section (header + body)
                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Column(
                      children: [
                        // Scrollable header
                        Container(
                          decoration: const BoxDecoration(
                            color: Color(0xFFF9FAFB),
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(6),
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 8),
                          child:
                              _buildScrollableFormTableHeader(context, title),
                        ),
                        // Scrollable body
                        Container(
                          constraints: const BoxConstraints(maxHeight: 200),
                          child: SingleChildScrollView(
                            child: Column(
                              children:
                                  _buildScrollableFormTableRows(context, title),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Fixed Actions column (header + body)
                Container(
                  // width: 120,
                  decoration: const BoxDecoration(
                    border: Border(
                      left: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // Fixed Actions header
                      Container(
                        decoration: const BoxDecoration(
                          color: Color(0xFFF9FAFB),
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(6),
                          ),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 14, vertical: 8),
                        child: Text(
                          'ACTIONS',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.labelMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.grey[700],
                          ),
                        ),
                      ),
                      // Fixed Actions body
                      Container(
                        constraints: const BoxConstraints(maxHeight: 200),
                        child: SingleChildScrollView(
                          child: Column(
                            children: _buildFixedActionsColumn(context, title),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScrollableFormTableHeader(BuildContext context, String title) {
    List<String> headers =
        _getTableHeaders(title); // Get headers without ACTIONS
    List<double> widths =
        _getColumnWidths(title); // Get widths without actions column

    return Row(
      children: headers.asMap().entries.map((entry) {
        int index = entry.key;
        String header = entry.value;
        double width = widths[index];

        return SizedBox(
          width: width,
          child: Align(
            alignment: Alignment.centerLeft,
            child: Text(
              header,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.labelMedium(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[700],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  List<Widget> _buildFixedActionsColumn(BuildContext context, String title) {
    List<List<String>> rowsData = _getTableData(title);

    return rowsData.asMap().entries.map((entry) {
      int index = entry.key;
      List<String> rowData = entry.value;

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 8),
        decoration: const BoxDecoration(
          border: Border(
            top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            IconButton(
              onPressed: () => _showEditModal(context, index, title, rowData),
              icon: const Icon(Icons.edit_outlined),
              color: Colors.blue[600],
              iconSize: 18,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              tooltip: 'Edit',
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: () => _showDeleteConfirmationGeneric(
                  context, index, title, rowData[0]),
              icon: const Icon(Icons.delete_outline),
              color: Colors.red[600],
              iconSize: 18,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
              tooltip: 'Delete',
            ),
          ],
        ),
      );
    }).toList();
  }

  List<List<String>> _getTableData(String title) {
    switch (title) {
      case 'Object Details':
        return [
          [
            'object_name',
            'Customer',
            'string',
            'YES',
            'Primary object identifier'
          ],
          [
            'created_date',
            '2024-01-15',
            'date',
            'YES',
            'Object creation timestamp'
          ],
          ['version', '1.0', 'string', 'NO', 'Object version number'],
        ];
      case 'Entity Relationships':
        return [
          ['customer_orders', 'Order', 'One-to-Many', '1:N', 'Active'],
          ['customer_address', 'Address', 'One-to-One', '1:1', 'Pending'],
          ['customer_payments', 'Payment', 'One-to-Many', '1:N', 'Active'],
        ];
      case 'Attribute Business Rules':
        return [
          ['email_validation', 'email', 'format = email', 'reject', 'High'],
          ['age_check', 'age', 'value >= 18', 'warn', 'Medium'],
          ['phone_format', 'phone', 'length = 10', 'format', 'Low'],
        ];
      case 'Enumerated Values':
        return [
          [
            'status_enum',
            'Active, Inactive, Pending',
            'Active',
            'Customer status options',
            'Active'
          ],
          [
            'type_enum',
            'Individual, Corporate',
            'Individual',
            'Customer type classification',
            'Active'
          ],
          [
            'priority_enum',
            'Low, Medium, High',
            'Medium',
            'Priority levels',
            'Active'
          ],
        ];
      case 'System Permissions':
        return [
          ['read_customer', 'User', 'Read', 'Customer Data', 'Active'],
          ['write_customer', 'Admin', 'Write', 'Customer Data', 'Active'],
          [
            'delete_customer',
            'SuperAdmin',
            'Delete',
            'Customer Data',
            'Restricted'
          ],
        ];
      case 'Security Classification':
        return [
          [
            'PII_Data',
            'High',
            'email, phone, address',
            'Encryption Required',
            'Active'
          ],
          ['Public_Data', 'Low', 'name, company', 'No Restrictions', 'Active'],
          [
            'Financial_Data',
            'Critical',
            'payment_info',
            'Strict Access Control',
            'Active'
          ],
        ];
      default:
        return [
          ['sample_name', 'sample_value', 'string', 'Active'],
          ['example_item', 'example_data', 'number', 'Pending'],
        ];
    }
  }

  Widget _buildBottomActions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Left side icons in one container - stacked vertically
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Color(0xAAD0D0D0), width: 1),
              boxShadow: [
                BoxShadow(
                  color: Colors.black12,
                  offset: const Offset(0, 3), // X: 0, Y: 3
                  blurRadius: 20, // Blur: 20
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.menu,
                  size: 20,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(height: 8),
                Icon(
                  Icons.mic_none,
                  size: 20,
                  color: Colors.grey.shade600,
                ),
              ],
            ),
          ),

          const Spacer(),

          // Right side colored circles - stacked vertically
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          const AllSmartResolutionObjectMobile(),
                    ),
                  );
                },
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: const BoxDecoration(
                    color: Color(0xFF00BCD4),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ],
                    shape: BoxShape.circle,
                    border: Border.fromBorderSide(
                      BorderSide(
                        color: Colors.white,
                        width: 2,
                      ),
                    ),
                  ),
                  child: const Icon(
                    Icons.auto_awesome,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: 50,
                height: 50,
                decoration: const BoxDecoration(
                  color: Color(0xFF673AB7),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 10,
                      offset: Offset(0, 4),
                    ),
                  ], // Purple color
                  shape: BoxShape.circle,
                  border: Border.fromBorderSide(
                    BorderSide(
                      color: Colors.white,
                      width: 2,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildObjectHeader(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                'Object: Customer',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.titleMedium(context),
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                  fontWeight: FontWeight.w600,
                  height: 1.2,
                ),
              ),
            ),
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: GestureDetector(
                onTap: () => _showNotificationPopup(context),
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.red.shade100,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.notifications_none,
                    size: 14,
                    color: Colors.red,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showNotificationPopup(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Message text
                Text(
                  'This Object is already exists in your library. You need to rename the objects to proceed.',
                  textAlign: TextAlign.center,
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodyMedium(context),
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.black,
                    fontWeight: FontWeight.w400,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 24),

                // Buttons row
                Row(
                  children: [
                    // Continue button
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: Colors.grey.shade300),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                        child: Text(
                          'Continue',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),

                    // Resolve button
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          // Navigate to AllSmartResolutionObjectMobile screen
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) =>
                                  const AllSmartResolutionObjectMobile(),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF0058FF),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          elevation: 0,
                        ),
                        child: Text(
                          'Resolve',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showEditModal(
      BuildContext context, int index, String title, List<String> rowData) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            width: 600,
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Modal header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Edit ${_getModalTitle(title)}',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleLarge(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      iconSize: 24,
                      color: Colors.grey[600],
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
                const SizedBox(height: 32),

                // Form fields based on section type with pre-filled values
                ..._buildModalFormFieldsWithValues(context, title, rowData),

                const SizedBox(height: 40),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(color: Colors.grey[300]!),
                        ),
                      ),
                      child: Text(
                        'Cancel',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF0058FF),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        'Update',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildModalFormFieldsWithValues(
      BuildContext context, String title, List<String> rowData) {
    switch (title) {
      case 'Entity Relationships':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Relationship Name', rowData[0])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Target Entity', rowData[1])),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Type',
                      ['One-to-One', 'One-to-Many', 'Many-to-Many'],
                      rowData[2])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownFieldWithValue(context,
                      'Cardinality', ['1:1', '1:N', 'N:M'], rowData[3])),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalDropdownFieldWithValue(
              context, 'Status', ['Active', 'Pending', 'Inactive'], rowData[4]),
        ];
      default:
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Name', rowData[0])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormFieldWithValue(
                      context, 'Value', rowData.length > 1 ? rowData[1] : '')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Type',
                      ['string', 'number', 'boolean'],
                      rowData.length > 2 ? rowData[2] : 'string')),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownFieldWithValue(
                      context,
                      'Status',
                      ['Active', 'Inactive'],
                      rowData.length > 3 ? rowData[3] : 'Active')),
            ],
          ),
        ];
    }
  }

  List<Widget> _buildModalFormFields(BuildContext context, String title) {
    switch (title) {
      case 'Object Details':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Property Name', 'property_name')),
              const SizedBox(width: 24),
              Expanded(
                  child:
                      _buildModalFormField(context, 'Value', 'Property Value')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Type',
                      ['string', 'number', 'boolean', 'date'])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Required', ['No', 'Yes'])),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalFormField(context, 'Description', 'Property description'),
        ];
      case 'Entity Relationships':
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(
                      context, 'Relationship Name', 'relationship_name')),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormField(
                      context, 'Target Entity', 'Target Entity')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(context, 'Type',
                      ['One-to-One', 'One-to-Many', 'Many-to-Many'])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Cardinality', ['1:1', '1:N', 'N:M'])),
            ],
          ),
          const SizedBox(height: 20),
          _buildModalDropdownField(
              context, 'Status', ['Active', 'Pending', 'Inactive']),
        ];
      default:
        return [
          Row(
            children: [
              Expanded(
                  child: _buildModalFormField(context, 'Name', 'Enter name')),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalFormField(context, 'Value', 'Enter value')),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Type', ['string', 'number', 'boolean'])),
              const SizedBox(width: 24),
              Expanded(
                  child: _buildModalDropdownField(
                      context, 'Status', ['Active', 'Inactive'])),
            ],
          ),
        ];
    }
  }

  Widget _buildModalFormField(BuildContext context, String label, String hint) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[500],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildModalDropdownField(
      BuildContext context, String label, List<String> options) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          icon: Container(
            alignment: Alignment.centerRight,
            child: const Icon(
              Icons.keyboard_arrow_down,
              size: 24,
            ),
          ),
          iconSize: 24,
          value: options.first,
          items: options.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            // Handle dropdown change
          },
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildModalDropdownFieldWithValue(BuildContext context, String label,
      List<String> options, String selectedValue) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          icon: Container(
            alignment: Alignment.centerRight,
            child: const Icon(
              Icons.keyboard_arrow_down,
              size: 24,
            ),
          ),
          iconSize: 24,
          value:
              options.contains(selectedValue) ? selectedValue : options.first,
          items: options.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: (String? newValue) {
            // Handle dropdown change
          },
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildModalFormFieldWithValue(
      BuildContext context, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          initialValue: value,
          decoration: InputDecoration(
            hintStyle: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[500],
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            filled: true,
            fillColor: Colors.white,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  void _showDeleteConfirmationGeneric(
      BuildContext context, int index, String title, String itemName) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Text(
            'Delete ${_getSingularTitle(title)}',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete "$itemName"? This action cannot be undone.',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[700],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // Handle delete logic here
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  String _getPlaceholderTitle(String title) {
    switch (title) {
      case 'Object Details':
        return 'Object Details Configuration';
      case 'Entity Relationships':
        return 'Entity Relationships Configuration';
      case 'Attribute Business Rules':
        return 'Business Rules Configuration';
      case 'Enumerated Values':
        return 'Enumerated Values Configuration';
      case 'System Permissions':
        return 'System Permissions Configuration';
      case 'Security Classification':
        return 'Security Classification Configuration';
      default:
        return '$title Configuration';
    }
  }

  String _getPlaceholderDescription(String title) {
    switch (title) {
      case 'Object Details':
        return 'Configure detailed information about the object including its properties, metadata, and general settings.';
      case 'Entity Relationships':
        return 'Define relationships between this entity and other entities in your system.';
      case 'Attribute Business Rules':
        return 'Set up business rules and validation logic for the attributes of this entity.';
      case 'Enumerated Values':
        return 'Configure predefined values and options for enumerated fields.';
      case 'System Permissions':
        return 'Define access control and permission settings for this entity.';
      case 'Security Classification':
        return 'Set security levels and classification rules for data protection.';
      default:
        return 'Configure settings and options for this section.';
    }
  }

  String _getAddButtonText(String title) {
    switch (title) {
      case 'Object Details':
        return 'Add Property';
      case 'Entity Relationships':
        return 'Add Relationship';
      case 'Attribute Business Rules':
        return 'Add Rule';
      case 'Enumerated Values':
        return 'Add Enum';
      case 'System Permissions':
        return 'Add Permission';
      case 'Security Classification':
        return 'Add Classification';
      default:
        return 'Add Item';
    }
  }

  void _showAddModal(BuildContext context, String title) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            width: 600,
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Modal header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _getModalTitle(title),
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleLarge(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                      iconSize: 24,
                      color: Colors.grey[600],
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
                const SizedBox(height: 32),

                // Form fields based on section type
                ..._buildModalFormFields(context, title),

                const SizedBox(height: 40),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(color: Colors.grey[300]!),
                        ),
                      ),
                      child: Text(
                        'Cancel',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF0058FF),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        'Apply This',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getSingularTitle(String title) {
    switch (title) {
      case 'Entity Relationships':
        return 'Relationship';
      case 'Object Details':
        return 'Property';
      case 'Attribute Business Rules':
        return 'Rule';
      case 'Enumerated Values':
        return 'Enum';
      case 'System Permissions':
        return 'Permission';
      case 'Security Classification':
        return 'Classification';
      default:
        return 'Item';
    }
  }

  void _showAddAttributeModal(BuildContext context) {
    final attributeNameController = TextEditingController();
    final displayNameController = TextEditingController();
    String selectedDataType = 'string';
    String selectedRequired = 'NO';
    String selectedUnique = 'NO';

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Add Attribute',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleMedium(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Attribute Name
                    _buildMobileFormField(
                        context, 'Attribute Name', attributeNameController),
                    const SizedBox(height: 12),

                    // Display Name
                    _buildMobileFormField(
                        context, 'Display Name', displayNameController),
                    const SizedBox(height: 12),

                    // Data Type
                    _buildMobileDropdownField(
                        context,
                        'Data Type',
                        ['string', 'number', 'boolean', 'date'],
                        selectedDataType, (value) {
                      setState(() {
                        selectedDataType = value!;
                      });
                    }),
                    const SizedBox(height: 12),

                    // Required and Unique in a row
                    Row(
                      children: [
                        Expanded(
                          child: _buildMobileDropdownField(context, 'Required',
                              ['NO', 'YES'], selectedRequired, (value) {
                            setState(() {
                              selectedRequired = value!;
                            });
                          }),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildMobileDropdownField(
                              context, 'Unique', ['NO', 'YES'], selectedUnique,
                              (value) {
                            setState(() {
                              selectedUnique = value!;
                            });
                          }),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(color: Colors.grey[300]!),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            child: Text(
                              'Cancel',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey[700],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              if (attributeNameController.text.isNotEmpty &&
                                  displayNameController.text.isNotEmpty) {
                                this.setState(() {
                                  _attributeData.add({
                                    'attributeName':
                                        attributeNameController.text,
                                    'displayName': displayNameController.text,
                                    'dataType': selectedDataType,
                                    'required': selectedRequired,
                                    'unique': selectedUnique,
                                  });
                                });
                                Navigator.of(context).pop();
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0058FF),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              elevation: 0,
                            ),
                            child: Text(
                              'Add',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _showEditAttributeModal(
      BuildContext context,
      int index,
      String attributeName,
      String displayName,
      String dataType,
      String required,
      String unique) {
    final attributeNameController = TextEditingController(text: attributeName);
    final displayNameController = TextEditingController(text: displayName);
    String selectedDataType = dataType;
    String selectedRequired = required;
    String selectedUnique = unique;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Edit Attribute',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.titleMedium(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Attribute Name
                    _buildMobileFormField(
                        context, 'Attribute Name', attributeNameController),
                    const SizedBox(height: 12),

                    // Display Name
                    _buildMobileFormField(
                        context, 'Display Name', displayNameController),
                    const SizedBox(height: 12),

                    // Data Type
                    _buildMobileDropdownField(
                        context,
                        'Data Type',
                        ['string', 'number', 'boolean', 'date'],
                        selectedDataType, (value) {
                      setState(() {
                        selectedDataType = value!;
                      });
                    }),
                    const SizedBox(height: 12),

                    // Required and Unique in a row
                    Row(
                      children: [
                        Expanded(
                          child: _buildMobileDropdownField(context, 'Required',
                              ['NO', 'YES'], selectedRequired, (value) {
                            setState(() {
                              selectedRequired = value!;
                            });
                          }),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildMobileDropdownField(
                              context, 'Unique', ['NO', 'YES'], selectedUnique,
                              (value) {
                            setState(() {
                              selectedUnique = value!;
                            });
                          }),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(color: Colors.grey[300]!),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            child: Text(
                              'Cancel',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey[700],
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              setState(() {
                                _attributeData[index] = {
                                  'attributeName': attributeNameController.text,
                                  'displayName': displayNameController.text,
                                  'dataType': selectedDataType,
                                  'required': selectedRequired,
                                  'unique': selectedUnique,
                                };
                              });
                              Navigator.of(context).pop();
                              this.setState(() {});
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0058FF),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              elevation: 0,
                            ),
                            child: Text(
                              'Update',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _deleteAttribute(BuildContext context, int index, String attributeName) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          title: Text(
            'Delete Attribute',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.titleMedium(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete the attribute "$attributeName"? This action cannot be undone.',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[700],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[700],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _attributeData.removeAt(index);
                });
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMobileFormField(
      BuildContext context, String label, TextEditingController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 6),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            filled: true,
            fillColor: Colors.white,
          ),
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildMobileDropdownField(BuildContext context, String label,
      List<String> options, String selectedValue, Function(String?) onChanged) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 6),
        DropdownButtonFormField<String>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            filled: true,
            fillColor: Colors.white,
          ),
          value:
              options.contains(selectedValue) ? selectedValue : options.first,
          items: options.map((String value) {
            return DropdownMenuItem<String>(
              value: value,
              child: Text(
                value,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
            );
          }).toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }
}

String _getModalTitle(String title) {
  switch (title) {
    case 'Object Details':
      return 'Add Object Property';
    case 'Entity Relationships':
      return 'Add Entity Relationship';
    case 'Attribute Business Rules':
      return 'Add Business Rule';
    case 'Enumerated Values':
      return 'Add Enumerated Value';
    case 'System Permissions':
      return 'Add System Permission';
    case 'Security Classification':
      return 'Add Security Classification';
    default:
      return 'Add Configuration';
  }
}

List<List<String>> _getTableData(String title) {
  switch (title) {
    case 'Object Details':
      return [
        [
          'object_name',
          'Customer',
          'string',
          'YES',
          'Primary object identifier'
        ],
        [
          'created_date',
          '2024-01-15',
          'date',
          'YES',
          'Object creation timestamp'
        ],
        ['version', '1.0', 'string', 'NO', 'Object version number'],
      ];
    case 'Entity Relationships':
      return [
        ['customer_orders', 'Order', 'One-to-Many', '1:N', 'Active'],
        ['customer_address', 'Address', 'One-to-One', '1:1', 'Pending'],
        ['customer_payments', 'Payment', 'One-to-Many', '1:N', 'Active'],
      ];
    case 'Attribute Business Rules':
      return [
        ['email_validation', 'email', 'format = email', 'reject', 'High'],
        ['age_check', 'age', 'value >= 18', 'warn', 'Medium'],
        ['phone_format', 'phone', 'length = 10', 'format', 'Low'],
      ];
    case 'Enumerated Values':
      return [
        [
          'status_enum',
          'Active, Inactive, Pending',
          'Active',
          'Customer status options',
          'Active'
        ],
        [
          'type_enum',
          'Individual, Corporate',
          'Individual',
          'Customer type classification',
          'Active'
        ],
        [
          'priority_enum',
          'Low, Medium, High',
          'Medium',
          'Priority levels',
          'Active'
        ],
      ];
    case 'System Permissions':
      return [
        ['read_customer', 'User', 'Read', 'Customer Data', 'Active'],
        ['write_customer', 'Admin', 'Write', 'Customer Data', 'Active'],
        [
          'delete_customer',
          'SuperAdmin',
          'Delete',
          'Customer Data',
          'Restricted'
        ],
      ];
    case 'Security Classification':
      return [
        [
          'PII_Data',
          'High',
          'email, phone, address',
          'Encryption Required',
          'Active'
        ],
        ['Public_Data', 'Low', 'name, company', 'No Restrictions', 'Active'],
        [
          'Financial_Data',
          'Critical',
          'payment_info',
          'Strict Access Control',
          'Active'
        ],
      ];
    default:
      return [
        ['sample_name', 'sample_value', 'string', 'Active'],
        ['example_item', 'example_data', 'number', 'Pending'],
      ];
  }
}

List<Widget> _buildScrollableFormTableRows(BuildContext context, String title) {
  List<List<String>> rowsData = _getTableData(title);
  List<double> widths = _getColumnWidths(title);

  return rowsData.map((rowData) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
        ),
      ),
      child: Row(
        children: rowData.asMap().entries.map((entry) {
          int colIndex = entry.key;
          String data = entry.value;
          double width = widths[colIndex];

          return SizedBox(
            width: width,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                data,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.labelMedium(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }).toList();
}

List<double> _getColumnWidths(String title) {
  switch (title) {
    case 'Object Details':
      return [150, 150, 100, 100, 245];
    case 'Entity Relationships':
      return [220, 180, 140, 120, 110];
    case 'Attribute Business Rules':
      return [220, 150, 150, 120, 110];
    case 'Enumerated Values':
      return [150, 200, 100, 200, 100];
    case 'System Permissions':
      return [200, 150, 150, 150, 100];
    case 'Security Classification':
      return [150, 100, 200, 200, 100];
    default:
      return [200, 200, 100, 100];
  }
}

List<String> _getTableHeaders(String title) {
  switch (title) {
    case 'Object Details':
      return ['PROPERTY NAME', 'VALUE', 'TYPE', 'REQUIRED', 'DESCRIPTION'];
    case 'Entity Relationships':
      return [
        'RELATIONSHIP NAME',
        'TARGET ENTITY',
        'TYPE',
        'CARDINALITY',
        'STATUS'
      ];
    case 'Attribute Business Rules':
      return ['RULE NAME', 'ATTRIBUTE', 'CONDITION', 'ACTION', 'PRIORITY'];
    case 'Enumerated Values':
      return ['ENUM NAME', 'VALUES', 'DEFAULT', 'DESCRIPTION', 'STATUS'];
    case 'System Permissions':
      return ['PERMISSION NAME', 'ROLE', 'ACCESS LEVEL', 'RESOURCE', 'STATUS'];
    case 'Security Classification':
      return [
        'CLASSIFICATION',
        'LEVEL',
        'ATTRIBUTES',
        'RESTRICTIONS',
        'STATUS'
      ];
    default:
      return ['NAME', 'VALUE', 'TYPE', 'STATUS'];
  }
}
