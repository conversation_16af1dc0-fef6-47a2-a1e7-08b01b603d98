import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';

/// A customizable timer widget with play/pause functionality.
///
/// This widget provides three states:
/// 1. Default state: Shows "00:00" with play icon
/// 2. Running state: Shows "Timer" + actual time with pause icon
/// 3. Paused state: Shows only the paused time with play icon
class ClockWidget extends StatefulWidget {
  /// The text to display when timer is not running
  final String labelText;

  /// Initial duration of the timer in seconds
  final int durationInSeconds;

  /// Whether the timer counts up or down
  final bool countUp;

  /// Whether to automatically start the timer when widget is displayed
  final bool autoStart;

  /// Whether to show the progress bar
  final bool showProgressBar;

  /// Background color of the widget
  final Color backgroundColor;

  /// Text color
  final Color textColor;

  /// Border color
  final Color? borderColor;

  /// Border width
  final double borderWidth;

  /// Border radius
  final double borderRadius;

  /// Padding inside the widget
  final EdgeInsetsGeometry padding;

  /// Width of the widget
  final double? width;

  /// Height of the widget
  final double? height;

  /// Font size of the text
  final double fontSize;

  /// Font weight of the text
  final FontWeight fontWeight;

  /// Color of the play/pause icon
  final Color iconColor;

  /// Size of the play/pause icon
  final double iconSize;

  /// Color of the progress bar
  final Color progressBarColor;

  /// Background color of the progress bar
  final Color progressBarBackgroundColor;

  /// Height of the progress bar
  final double progressBarHeight;

  /// Tooltip text to display on hover
  final String? tooltip;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  /// Callback when timer starts
  final VoidCallback? onStart;

  /// Callback when timer pauses
  final VoidCallback? onPause;

  /// Callback when timer completes
  final VoidCallback? onComplete;

  /// Callback when timer ticks
  final Function(int)? onTick;

  const ClockWidget({
    super.key,
    this.labelText = 'Timer',
    this.durationInSeconds = 60,
    this.countUp = true,
    this.autoStart = true,
    this.showProgressBar = true,
    this.backgroundColor = Colors.white,
    this.textColor = const Color(0xFF333333),
    this.borderColor = const Color(0xFF0058FF),
    this.borderWidth = 1.0,
    this.borderRadius = 4.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
    this.width,
    this.height,
    this.fontSize = 14.0,
    this.fontWeight = FontWeight.w500,
    this.iconColor = const Color(0xFF666666),
    this.iconSize = 20.0,
    this.progressBarColor = const Color(0xFF0058FF),
    this.progressBarBackgroundColor = const Color(0xFFE0E0E0),
    this.progressBarHeight = 4.0,
    this.tooltip,
    this.semanticsLabel,
    this.onStart,
    this.onPause,
    this.onComplete,
    this.onTick,
  });

  /// Creates a ClockWidget from a JSON map
  factory ClockWidget.fromJson(Map<String, dynamic> json) {
    Color? parseColor(dynamic colorValue) {
      if (colorValue == null) return null;

      if (colorValue is String) {
        if (colorValue.startsWith('#')) {
          try {
            final hexColor = colorValue.substring(1);
            final hexValue = int.parse(
              '0xFF${hexColor.padRight(8, 'F').substring(0, 8)}',
            );
            return Color(hexValue);
          } catch (e) {
            return null;
          }
        }

        switch (colorValue.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'blue':
            return Colors.blue;
          case 'green':
            return Colors.green;
          case 'yellow':
            return Colors.yellow;
          case 'orange':
            return Colors.orange;
          case 'purple':
            return Colors.purple;
          case 'pink':
            return Colors.pink;
          case 'brown':
            return Colors.brown;
          case 'grey':
          case 'gray':
            return Colors.grey;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'transparent':
            return Colors.transparent;
          default:
            return null;
        }
      } else if (colorValue is int) {
        return Color(colorValue);
      }

      return null;
    }

    FontWeight parseFontWeight(dynamic weightValue) {
      if (weightValue == null) return FontWeight.w500;

      if (weightValue is String) {
        switch (weightValue.toLowerCase()) {
          case 'bold':
            return FontWeight.bold;
          case 'normal':
            return FontWeight.normal;
          case 'light':
            return FontWeight.w300;
          case 'medium':
            return FontWeight.w500;
          case 'semibold':
            return FontWeight.w600;
          default:
            return FontWeight.w500;
        }
      } else if (weightValue is int) {
        switch (weightValue) {
          case 100:
            return FontWeight.w100;
          case 200:
            return FontWeight.w200;
          case 300:
            return FontWeight.w300;
          case 400:
            return FontWeight.w400;
          case 500:
            return FontWeight.w500;
          case 600:
            return FontWeight.w600;
          case 700:
            return FontWeight.w700;
          case 800:
            return FontWeight.w800;
          case 900:
            return FontWeight.w900;
          default:
            return FontWeight.w500;
        }
      }

      return FontWeight.w500;
    }

    EdgeInsetsGeometry parsePadding(dynamic paddingValue) {
      if (paddingValue == null)
        return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);

      if (paddingValue is num) {
        return EdgeInsets.all(paddingValue.toDouble());
      } else if (paddingValue is Map<String, dynamic>) {
        final left = (paddingValue['left'] as num?)?.toDouble() ?? 12.0;
        final top = (paddingValue['top'] as num?)?.toDouble() ?? 8.0;
        final right = (paddingValue['right'] as num?)?.toDouble() ?? 12.0;
        final bottom = (paddingValue['bottom'] as num?)?.toDouble() ?? 8.0;
        return EdgeInsets.fromLTRB(left, top, right, bottom);
      }

      return const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0);
    }

    return ClockWidget(
      labelText: json['labelText'] as String? ?? 'Timer',
      durationInSeconds: json['durationInSeconds'] as int? ?? 60,
      countUp: json['countUp'] as bool? ?? true,
      autoStart: json['autoStart'] as bool? ?? true,
      showProgressBar: json['showProgressBar'] as bool? ?? true,
      backgroundColor: parseColor(json['backgroundColor']) ?? Colors.white,
      textColor: parseColor(json['textColor']) ?? const Color(0xFF333333),
      borderColor: parseColor(json['borderColor']) ?? const Color(0xFF0058FF),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      padding: parsePadding(json['padding']),
      width: (json['width'] as num?)?.toDouble(),
      height: (json['height'] as num?)?.toDouble(),
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 14.0,
      fontWeight: parseFontWeight(json['fontWeight']),
      iconColor: parseColor(json['iconColor']) ?? const Color(0xFF666666),
      iconSize: (json['iconSize'] as num?)?.toDouble() ?? 20.0,
      progressBarColor:
          parseColor(json['progressBarColor']) ?? const Color(0xFF0058FF),
      progressBarBackgroundColor:
          parseColor(json['progressBarBackgroundColor']) ??
          const Color(0xFFE0E0E0),
      progressBarHeight: (json['progressBarHeight'] as num?)?.toDouble() ?? 4.0,
      tooltip: json['tooltip'] as String?,
      semanticsLabel: json['semanticsLabel'] as String?,
      onStart:
          json['onStart'] == true
              ? () {
                debugPrint('Timer started');
              }
              : null,
      onPause:
          json['onPause'] == true
              ? () {
                debugPrint('Timer paused');
              }
              : null,
      onComplete:
          json['onComplete'] == true
              ? () {
                debugPrint('Timer completed');
              }
              : null,
      onTick:
          json['onTick'] == true
              ? (seconds) {
                debugPrint('Timer tick: $seconds seconds');
              }
              : null,
    );
  }

  @override
  State<ClockWidget> createState() => _ClockWidgetState();
}

class _ClockWidgetState extends State<ClockWidget> {
  Timer? _timer;
  bool _isRunning = false;
  bool _isPaused = false;
  bool _isHovered = false;
  int _currentSeconds = 0;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _currentSeconds = widget.countUp ? 0 : widget.durationInSeconds;
    _focusNode = FocusNode();

    // Auto-start the timer if autoStart is enabled
    if (widget.autoStart) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _startTimer();
      });
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    _focusNode.dispose();
    super.dispose();
  }

  void _toggleTimer() {
    if (_isRunning) {
      _pauseTimer();
    } else {
      _startTimer();
    }
  }

  void _startTimer() {
    if (_isRunning) return;

    setState(() {
      _isRunning = true;
      _isPaused = false;
    });

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (widget.countUp) {
          _currentSeconds++;

          if (_currentSeconds >= widget.durationInSeconds) {
            _completeTimer();
          }
        } else {
          _currentSeconds--;

          if (_currentSeconds <= 0) {
            _completeTimer();
          }
        }
      });

      if (widget.onTick != null) {
        widget.onTick!(_currentSeconds);
      }
    });

    if (widget.onStart != null) {
      widget.onStart!();
    }
  }

  void _pauseTimer() {
    if (!_isRunning) return;

    _timer?.cancel();
    setState(() {
      _isRunning = false;
      _isPaused = true;
    });

    if (widget.onPause != null) {
      widget.onPause!();
    }
  }

  void _completeTimer() {
    _timer?.cancel();
    setState(() {
      _isRunning = false;
    });

    if (widget.onComplete != null) {
      widget.onComplete!();
    }
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  void _handleHoverChange(bool isHovered) {
    if (_isHovered != isHovered) {
      setState(() {
        _isHovered = isHovered;
      });
    }
  }

  double _getResponsiveHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 56.0; // Extra Large (>1920px)
    } else if (screenWidth >= 1440) {
      return 48.0; // Large (1440-1920px)
    } else if (screenWidth >= 1280) {
      return 40.0; // Medium (1280-1366px)
    } else if (screenWidth >= 768) {
      return 40.0; // Small (768-1024px)
    } else {
      return 40.0; // Default for very small screens
    }
  }

  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 14.0; // Medium
    } else {
      return 12.0; // Default for very small screens
    }
  }

  @override
  Widget build(BuildContext context) {
    // Determine what text to show based on state
    String displayText;
    bool showTime = false;

    if (!_isRunning && !_isPaused) {
      // Default state: show "00:00"
      displayText = "00:00";
    } else if (_isRunning) {
      // Running state: show "Timer" + time
      displayText = widget.labelText;
      showTime = true;
    } else {
      // Paused state: show only time, no label
      displayText = _formatTime(_currentSeconds);
    }

    // Create the base container with consistent styling
    Widget containerWidget = Container(
      width: double.infinity,
      height: _getResponsiveHeight(context),
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 0.0),
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: BorderRadius.circular(4.0),
        border: Border.all(
          color: _isHovered ? const Color(0xFF0058FF) : const Color(0xFFCCCCCC),
          width: widget.borderWidth,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Text content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  _formatTime(_currentSeconds),
                  // style: TextStyle(
                  //   color: widget.textColor,
                  //   fontSize: _getResponsiveInputFontSize(context),
                  //   fontWeight: widget.fontWeight,
                  // ),
                  style: FontManager.getCustomStyle(
                    fontFamily: FontManager.fontFamilyInter,
                    fontWeight: FontManager.medium,
                    //color: widget.textColor,
                    color: Color(0xFF333333),
                    fontSize: _getResponsiveFontSize(context),
                  ),
                ),
              ],
            ),
          ),

          // Play/Pause button
          GestureDetector(
            onTap: _toggleTimer,
            child: Container(
              //padding: const EdgeInsets.all(0.0), // space between icon and border
              // decoration: BoxDecoration(
              //   border: Border.all(
              //     color: _isHovered ? const Color(0xFF0058FF) : const Color(0xFFCCCCCC), // same as icon color
              //     width: 1.0, // border width
              //   ),
              //   borderRadius: BorderRadius.circular(1000.0), // optional: rounded corners
              // ),
              // child: Icon(
              //   _isRunning ? Icons.pause : Icons.play_arrow,
              //   color: _isHovered ? const Color(0xFF0058FF) : const Color(0xFFCCCCCC),
              //   size: _getResponsiveIconSize(context),
              // ),
              child: SvgPicture.asset(
                _isHovered
                    ? 'assets/images/cu-clock-hover.svg'
                    : 'assets/images/cu-clock.svg',
                package: 'ui_controls_library',
                //width: _getResponsiveIconSize(context),
              ),
            ),
          ),
        ],
      ),
    );

    // Add gesture detector for advanced interactions
    Widget gestureWidget = GestureDetector(
      onTap: _toggleTimer,
      child: containerWidget,
    );

    // Add mouse region for hover detection
    Widget hoverWidget = MouseRegion(
      onEnter: (_) => _handleHoverChange(true),
      onExit: (_) => _handleHoverChange(false),
      child: gestureWidget,
    );

    // Add focus handling
    Widget focusWidget = Focus(focusNode: _focusNode, child: hoverWidget);

    // Add tooltip if needed
    if (widget.tooltip != null) {
      focusWidget = Tooltip(message: widget.tooltip!, child: focusWidget);
    }

    // Add semantics if needed
    if (widget.semanticsLabel != null) {
      focusWidget = Semantics(
        label: widget.semanticsLabel,
        value: displayText,
        child: focusWidget,
      );
    }

    return focusWidget;
  }
}

double _getResponsiveIconSize(BuildContext context) {
  final double screenWidth = MediaQuery.of(context).size.width;
  if (screenWidth > 1920) {
    return 24.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 22.0; // Large
  } else if (screenWidth >= 1280) {
    return 24.0; // Medium
  } else if (screenWidth >= 768) {
    return 18.0; // Small
  } else {
    return 16.0; // Extra Small (fallback for very small screens)
  }
}
