import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'dart:convert';
import 'package:ui_controls_library/utils/callback_interpreter.dart';
import 'package:ui_controls_library/widgets/utils/font_manager.dart';

/// Format options for the date range display
enum DateRangeFormat {
  /// Standard format (e.g., "Jan 1, 2023 - Jan 7, 2023")
  standard,

  /// Short format (e.g., "01/01/2023 - 01/07/2023")
  short,

  /// Long format (e.g., "January 1, 2023 - January 7, 2023")
  long,

  /// ISO format (e.g., "2023-01-01 - 2023-01-07")
  iso,

  /// Custom format (specified by formatPattern)
  custom,
}

/// A comprehensive widget for selecting and displaying a date range.
///
/// This widget provides extensive customization options for selecting
/// and displaying date ranges with various formats and styles.
class DateRangeWidget extends StatefulWidget {
  /// The initial start date value
  final DateTime? initialStartDate;

  /// The initial end date value
  final DateTime? initialEndDate;

  /// Whether to allow selecting dates
  final bool allowDateSelection;

  /// Format of the date range display
  final DateRangeFormat format;

  /// Custom format pattern (used when format is DateRangeFormat.custom)
  final String? formatPattern;

  /// Whether to show the weekday
  final bool showWeekday;

  /// Whether to show the year
  final bool showYear;

  /// Whether to show the month
  final bool showMonth;

  /// Whether to show the day
  final bool showDay;

  /// Locale for date formatting
  final String locale;

  /// Text style for the date range display
  final TextStyle? textStyle;

  /// Text color for the date range display
  final Color textColor;

  /// Background color for the widget
  final Color backgroundColor;

  /// Font size for the date range display
  final double fontSize;

  /// Font weight for the date range display
  final FontWeight fontWeight;

  /// Font family for the date range display
  final String? fontFamily;

  /// Text alignment for the date range display
  final TextAlign textAlign;

  /// Whether to show a border around the widget
  final bool hasBorder;

  /// Border radius for the widget
  final double borderRadius;

  /// Border color for the widget
  final Color borderColor;

  /// Border width for the widget
  final double borderWidth;

  /// Whether to show a shadow under the widget
  final bool hasShadow;

  /// Elevation for the shadow
  final double elevation;

  /// Whether to use a compact layout
  final bool isCompact;

  /// Label text to display above the date range
  final String? label;

  /// Optional prefix text
  final String? prefix;

  /// Optional suffix text
  final String? suffix;

  /// Whether to show a calendar icon
  final bool showCalendarIcon;

  /// Icon to use for the calendar
  final IconData calendarIcon;

  /// Color for the calendar icon
  final Color? calendarIconColor;

  /// Whether to use a dark theme
  final bool isDarkTheme;

  /// Whether the widget is enabled
  final bool enabled;

  /// Whether the widget is read-only
  final bool readOnly;

  /// Minimum selectable date
  final DateTime? minDate;

  /// Maximum selectable date
  final DateTime? maxDate;

  /// Callback when the date range changes
  final Function(DateTime startDate, DateTime endDate)? onChanged;

  /// Callback when the date range is selected
  final Function(DateTime startDate, DateTime endDate)? onSelected;

  // Advanced interaction properties
  /// Callback for mouse hover
  final void Function(bool)? onHover;

  /// Callback for keyboard focus
  final void Function(bool)? onFocus;

  /// Color when the widget is hovered
  final Color? hoverColor;

  /// Color when the widget is focused
  final Color? focusColor;

  /// Callback for tap gesture
  final VoidCallback? onTap;

  /// Callback for double tap gesture
  final VoidCallback? onDoubleTap;

  /// Callback for long press gesture
  final VoidCallback? onLongPress;

  /// Focus node for controlling focus
  final FocusNode? focusNode;

  /// Whether the widget should autofocus
  final bool autofocus;

  // JSON callback properties
  /// Dynamic callback definitions from JSON
  final Map<String, dynamic>? jsonCallbacks;

  /// Whether to use dynamic callbacks from JSON
  final bool useJsonCallbacks;

  /// State map for dynamic callbacks
  final Map<String, dynamic>? callbackState;

  /// Custom handlers for dynamic callbacks
  final Map<String, Function>? customCallbackHandlers;

  /// JSON configuration for the widget
  final Map<String, dynamic>? jsonConfig;

  /// Creates a date range widget.
  const DateRangeWidget({
    super.key,
    this.initialStartDate,
    this.initialEndDate,
    this.allowDateSelection = true,
    this.format = DateRangeFormat.standard,
    this.formatPattern,
    this.showWeekday = false,
    this.showYear = true,
    this.showMonth = true,
    this.showDay = true,
    this.locale = 'en_US',
    this.textStyle,
    this.textColor = Colors.black,
    this.backgroundColor = Colors.white,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.normal,
    this.fontFamily,
    this.textAlign = TextAlign.start,
    this.hasBorder = true,
    this.borderRadius = 4.0,
    this.borderColor = const Color(0xFFCCCCCC),
    this.borderWidth = 1.0,
    this.hasShadow = false,
    this.elevation = 2.0,
    this.isCompact = false,
    this.label,
    this.prefix,
    this.suffix,
    this.showCalendarIcon = true,
    this.calendarIcon = Icons.date_range,
    this.calendarIconColor,
    this.isDarkTheme = false,
    this.enabled = true,
    this.readOnly = false,
    this.minDate,
    this.maxDate,
    this.onChanged,
    this.onSelected,
    // Advanced interaction properties
    this.onHover,
    this.onFocus,
    this.hoverColor = const Color(0xFF0058FF),
    this.focusColor = const Color(0xFF0058FF),
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.focusNode,
    this.autofocus = false,
    // JSON callback properties
    this.jsonCallbacks,
    this.useJsonCallbacks = false,
    this.callbackState,
    this.customCallbackHandlers,
    this.jsonConfig,
  });

  /// Creates a DateRangeWidget from a JSON map
  factory DateRangeWidget.fromJson(Map<String, dynamic> json) {
    // Parse date format
    DateRangeFormat format = DateRangeFormat.standard;
    if (json['format'] != null) {
      switch (json['format'].toString().toLowerCase()) {
        case 'short':
          format = DateRangeFormat.short;
          break;
        case 'long':
          format = DateRangeFormat.long;
          break;
        case 'iso':
          format = DateRangeFormat.iso;
          break;
        case 'custom':
          format = DateRangeFormat.custom;
          break;
        case 'standard':
        default:
          format = DateRangeFormat.standard;
          break;
      }
    }

    // Parse text alignment
    TextAlign textAlign = TextAlign.start;
    if (json['textAlign'] != null) {
      switch (json['textAlign'].toString().toLowerCase()) {
        case 'center':
          textAlign = TextAlign.center;
          break;
        case 'left':
        case 'start':
          textAlign = TextAlign.left;
          break;
        case 'right':
        case 'end':
          textAlign = TextAlign.right;
          break;
        case 'justify':
          textAlign = TextAlign.justify;
          break;
      }
    }

    // Parse font weight
    FontWeight fontWeight = FontWeight.normal;
    if (json['fontWeight'] != null) {
      if (json['fontWeight'] == 'bold' || json['fontWeight'] == true) {
        fontWeight = FontWeight.bold;
      } else if (json['fontWeight'] == 'light') {
        fontWeight = FontWeight.w300;
      }
    }

    // Parse colors
    Color? textColor;
    if (json['textColor'] != null) {
      textColor = _colorFromJson(json['textColor']);
    }

    Color? backgroundColor;
    if (json['backgroundColor'] != null) {
      backgroundColor = _colorFromJson(json['backgroundColor']);
    }

    Color? borderColor;
    if (json['borderColor'] != null) {
      borderColor = _colorFromJson(json['borderColor']);
    }

    Color? hoverColor;
    if (json['hoverColor'] != null) {
      hoverColor = _colorFromJson(json['hoverColor']);
    }

    Color? focusColor;
    if (json['focusColor'] != null) {
      focusColor = _colorFromJson(json['focusColor']);
    }

    return DateRangeWidget(
      allowDateSelection: json['allowDateSelection'] as bool? ?? true,
      format: format,
      formatPattern: json['formatPattern'] as String?,
      showWeekday: json['showWeekday'] as bool? ?? false,
      showYear: json['showYear'] as bool? ?? true,
      showMonth: json['showMonth'] as bool? ?? true,
      showDay: json['showDay'] as bool? ?? true,
      locale: json['locale'] as String? ?? 'en_US',
      textColor: textColor ?? Colors.black,
      backgroundColor: backgroundColor ?? Colors.white,
      fontSize: (json['fontSize'] as num?)?.toDouble() ?? 16.0,
      fontWeight: fontWeight,
      fontFamily: json['fontFamily'] as String?,
      textAlign: textAlign,
      hasBorder: json['hasBorder'] as bool? ?? true,
      borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 4.0,
      borderColor: borderColor ?? const Color(0xFFCCCCCC),
      borderWidth: (json['borderWidth'] as num?)?.toDouble() ?? 1.0,
      hasShadow: json['hasShadow'] as bool? ?? false,
      elevation: (json['elevation'] as num?)?.toDouble() ?? 2.0,
      isCompact: json['isCompact'] as bool? ?? false,
      label: json['label'] as String?,
      prefix: json['prefix'] as String?,
      suffix: json['suffix'] as String?,
      showCalendarIcon: json['showCalendarIcon'] as bool? ?? true,
      isDarkTheme: json['isDarkTheme'] as bool? ?? false,
      enabled: json['enabled'] as bool? ?? true,
      readOnly: json['readOnly'] as bool? ?? false,
      hoverColor: hoverColor,
      focusColor: focusColor,
      useJsonCallbacks: json['useJsonCallbacks'] as bool? ?? false,
      jsonConfig: json,
    );
  }

  /// Converts a JSON color value to a Flutter Color
  static Color? _colorFromJson(dynamic colorValue) {
    if (colorValue == null) return null;

    if (colorValue is String) {
      // Handle hex strings like "#FF0000"
      if (colorValue.startsWith('#')) {
        String hexColor = colorValue.substring(1);

        // Handle shorthand hex like #RGB
        if (hexColor.length == 3) {
          hexColor = hexColor.split('').map((c) => '$c$c').join('');
        }

        // Add alpha channel if missing
        if (hexColor.length == 6) {
          hexColor = 'FF$hexColor';
        }

        // Parse the hex value
        try {
          return Color(int.parse('0x$hexColor'));
        } catch (e) {
          return null;
        }
      }

      // Handle named colors
      switch (colorValue.toLowerCase()) {
        case 'red':
          return Colors.red;
        case 'blue':
          return Colors.blue;
        case 'green':
          return Colors.green;
        case 'yellow':
          return Colors.yellow;
        case 'orange':
          return Colors.orange;
        case 'purple':
          return Colors.purple;
        case 'pink':
          return Colors.pink;
        case 'brown':
          return Colors.brown;
        case 'grey':
        case 'gray':
          return Colors.grey;
        case 'black':
          return Colors.black;
        case 'white':
          return Colors.white;
        default:
          return null;
      }
    } else if (colorValue is int) {
      return Color(colorValue);
    }

    return null;
  }

  @override
  State<DateRangeWidget> createState() => _DateRangeWidgetState();
}

class _DateRangeWidgetState extends State<DateRangeWidget> {
  late DateTime _startDate;
  late DateTime _endDate;
  String _formattedDateRange = '';
  bool _hasUserSelectedDateRange = false;

  // State for hover and focus
  bool _isHovered = false;
  bool _hasFocus = false;

  // State for dynamic callbacks
  late Map<String, dynamic> _callbackState;

  @override
  void initState() {
    super.initState();
    _startDate = widget.initialStartDate ?? DateTime.now();
    _endDate =
        widget.initialEndDate ?? DateTime.now().add(const Duration(days: 7));
    _hasUserSelectedDateRange =
        widget.initialStartDate != null && widget.initialEndDate != null;
    _updateFormattedDateRange();

    // Initialize callback state
    _callbackState = widget.callbackState?.cast<String, dynamic>() ?? {};
  }

  void _updateFormattedDateRange() {
    // Show placeholder if user hasn't selected a date range yet
    if (!_hasUserSelectedDateRange) {
      _formattedDateRange = 'DD/MM/YYYY - DD/MM/YYYY';
      return;
    }

    // Use DD/MM/YYYY format as default
    DateFormat formatter = DateFormat('dd/MM/yyyy');

    // Handle custom format if specified
    if (widget.format == DateRangeFormat.custom &&
        widget.formatPattern != null) {
      formatter = DateFormat(widget.formatPattern!, widget.locale);
    } else {
      // For all other cases, use DD/MM/YYYY format
      switch (widget.format) {
        case DateRangeFormat.short:
          formatter = DateFormat('dd/MM/yyyy');
          break;
        case DateRangeFormat.long:
          formatter = DateFormat('dd/MM/yyyy');
          break;
        case DateRangeFormat.iso:
          formatter = DateFormat('dd/MM/yyyy');
          break;
        case DateRangeFormat.standard:
        default:
          formatter = DateFormat('dd/MM/yyyy');
          break;
      }
    }

    // Format both start and end dates using DD/MM/YYYY format
    String formattedStartDate = formatter.format(_startDate);
    String formattedEndDate = formatter.format(_endDate);

    // Handle component visibility flags only if not all components are shown
    if (!widget.showYear || !widget.showMonth || !widget.showDay) {
      // Create custom format based on visibility flags
      List<String> startComponents = [];
      List<String> endComponents = [];

      if (widget.showWeekday) {
        startComponents.add(DateFormat.E(widget.locale).format(_startDate));
        endComponents.add(DateFormat.E(widget.locale).format(_endDate));
      }

      if (widget.showDay) {
        startComponents.add(DateFormat.d(widget.locale).format(_startDate));
        endComponents.add(DateFormat.d(widget.locale).format(_endDate));
      }

      if (widget.showMonth) {
        startComponents.add(DateFormat('MM').format(_startDate));
        endComponents.add(DateFormat('MM').format(_endDate));
      }

      if (widget.showYear) {
        startComponents.add(DateFormat.y(widget.locale).format(_startDate));
        endComponents.add(DateFormat.y(widget.locale).format(_endDate));
      }

      if (startComponents.isNotEmpty && endComponents.isNotEmpty) {
        formattedStartDate = startComponents.join('/');
        formattedEndDate = endComponents.join('/');
      }
    } else if (widget.showWeekday) {
      // Add weekday to the formatted dates
      String startWeekday = DateFormat.E(widget.locale).format(_startDate);
      String endWeekday = DateFormat.E(widget.locale).format(_endDate);
      formattedStartDate = '$startWeekday, $formattedStartDate';
      formattedEndDate = '$endWeekday, $formattedEndDate';
    }

    // Combine start and end dates with separator
    _formattedDateRange = '$formattedStartDate - $formattedEndDate';
  }

  /// Handles hover state changes
  void _onHoverChange(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
      if (widget.onHover != null) {
        widget.onHover!(isHovered);
      }

      // Execute dynamic callback if enabled
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onHover')) {
        final callback = widget.jsonCallbacks!['onHover'];
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: isHovered,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      }
    });
  }

  /// Handles focus state changes
  void _onFocusChange(bool hasFocus) {
    setState(() {
      _hasFocus = hasFocus;
      if (widget.onFocus != null) {
        widget.onFocus!(hasFocus);
      }

      // Execute dynamic callback if enabled
      if (widget.useJsonCallbacks &&
          widget.jsonCallbacks != null &&
          widget.jsonCallbacks!.containsKey('onFocus')) {
        final callback = widget.jsonCallbacks!['onFocus'];
        CallbackInterpreter.executeCallback(
          callback,
          context,
          value: hasFocus,
          state: _callbackState,
          customHandlers: widget.customCallbackHandlers,
        );
      }
    });
  }

  Future<void> _selectDateRange() async {
    if (!widget.enabled || widget.readOnly || !widget.allowDateSelection)
      return;

    final DateTimeRange? pickedRange = await showDateRangePicker(
      context: context,
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
      firstDate: widget.minDate ?? DateTime(1900),
      lastDate: widget.maxDate ?? DateTime(2100),
      builder: (context, child) {
        // return Theme(
        //   data: ThemeData(
        //     // useMaterial3: false,
        //     colorScheme: ColorScheme.light(
        //       primary: const Color(0xFF0058FF),
        //       onBackground: const Color(0xFFCCCCCC),
        //       secondary: const Color(0xFF0058FF),
        //       onSecondary: Colors.white,
        //     ),
        //   ),
        //   child: child ?? SizedBox(),
        // );
        return Theme(
          data: ThemeData(
            colorScheme: ColorScheme.light(
              primary: const Color(0xFF0058FF),
              onBackground: const Color(0xFFCCCCCC),
              secondary: const Color(0xFF0058FF),
              onSecondary: Colors.white,
            ),
            textTheme: TextTheme(
              bodyMedium: FontManager.getCustomStyle(
                fontFamily: FontManager.fontFamilyInter,
                fontWeight: FontManager.medium,
              ),
            ),
          ),
          child: child ?? const SizedBox(),
        );
      },
    );

    if (pickedRange != null) {
      setState(() {
        _startDate = pickedRange.start;
        _endDate = pickedRange.end;
        _hasUserSelectedDateRange = true;
        _updateFormattedDateRange();
      });

      if (widget.onChanged != null) {
        widget.onChanged!(_startDate, _endDate);
      }

      if (widget.onSelected != null) {
        widget.onSelected!(_startDate, _endDate);
      }

      if (widget.onTap != null) {
        widget.onTap!();
      }

      // Execute dynamic callbacks
      if (widget.useJsonCallbacks && widget.jsonCallbacks != null) {
        // Schedule callbacks to run after the async gap
        Future.microtask(() {
          if (!mounted) return;

          // Execute onChanged callback
          if (widget.jsonCallbacks!.containsKey('onChanged')) {
            final callback = widget.jsonCallbacks!['onChanged'];
            CallbackInterpreter.executeCallback(
              callback,
              context,
              value: {
                'startDate': _startDate.toString(),
                'endDate': _endDate.toString(),
              },
              state: _callbackState,
              customHandlers: widget.customCallbackHandlers,
            );
          }

          // Execute onSelected callback
          if (widget.jsonCallbacks!.containsKey('onSelected')) {
            final callback = widget.jsonCallbacks!['onSelected'];
            CallbackInterpreter.executeCallback(
              callback,
              context,
              value: {
                'startDate': _startDate.toString(),
                'endDate': _endDate.toString(),
              },
              state: _callbackState,
              customHandlers: widget.customCallbackHandlers,
            );
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Apply dark theme if specified
    final effectiveTextColor =
        widget.isDarkTheme ? Colors.white : widget.textColor;
    final effectiveBackgroundColor =
        widget.isDarkTheme ? Colors.grey.shade800 : widget.backgroundColor;

    // Create text style
    // TextStyle textStyle =
    //     widget.textStyle ??
    //     TextStyle(
    //       FontManager.getCustomStyle(
    //         fontFamily: FontManager.fontFamilyInter,
    //         fontWeight: FontManager.medium,
    //         color: effectiveTextColor,
    //         fontSize: _getResponsiveValueFontSize(context),
    //       ),
    //     );
    TextStyle textStyle =
        widget.textStyle ??
        FontManager.getCustomStyle(
          fontFamily: FontManager.fontFamilyInter,
          fontWeight: FontManager.medium,
          color: effectiveTextColor,
          fontSize: _getResponsiveValueFontSize(context),
        );
    // Get responsive font size based on screen width
    double getResponsiveTitleFontSize(double screenWidth) {
      if (screenWidth > 1920) {
        return 18.0; // Extra Large
      } else if (screenWidth >= 1440) {
        return 16.0; // Large
      } else if (screenWidth >= 1280) {
        return 14.0; // Medium
      } else {
        return 14.0; // Default for very small screens
      }
    }

    final double screenWidth = MediaQuery.of(context).size.width;
    final double responsiveTitleFontSize = getResponsiveTitleFontSize(
      screenWidth,
    );
    // Get responsive icon size based on screen width
    double getResponsiveIconSize(double screenWidth) {
      if (screenWidth > 1920) {
        return 22.0; // Extra Large
      } else if (screenWidth >= 1440) {
        return 20.0; // Large
      } else if (screenWidth >= 1280) {
        return 18.0; // Medium
      } else if (screenWidth >= 768) {
        return 16.0; // Small
      } else {
        return 12.0; // Extra Small (fallback for very small screens)
      }
    }

    final double responsiveIconSize = getResponsiveIconSize(screenWidth);
    // Create the main content widget
    Widget content = MouseRegion(
      onEnter: (_) => _onHoverChange(true),
      onExit: (_) => _onHoverChange(false),
      cursor: SystemMouseCursors.click,
      child: Focus(
        focusNode: widget.focusNode,
        autofocus: widget.autofocus,
        onFocusChange: _onFocusChange,
        child: GestureDetector(
          onTap: () => _selectDateRange(),
          onDoubleTap: widget.onDoubleTap,
          onLongPress: widget.onLongPress,
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: _getResponsiveHeight(context),
            padding: _getResponsivePadding(context),
            decoration: BoxDecoration(
              color: effectiveBackgroundColor,
              borderRadius: BorderRadius.circular(widget.borderRadius),
              border:
                  widget.hasBorder
                      ? Border.all(
                        color:
                            _isHovered
                                ? (widget.hoverColor ?? const Color(0xFF0058FF))
                                : _hasFocus
                                ? (widget.focusColor ?? const Color(0xFF0058FF))
                                : widget.borderColor,
                        width: widget.borderWidth,
                      )
                      : null,
              boxShadow:
                  widget.hasShadow
                      ? [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: widget.elevation,
                          offset: Offset(0, widget.elevation / 2),
                        ),
                      ]
                      : null,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (widget.prefix != null) ...[
                  Text(
                    widget.prefix!,
                    // style: TextStyle(
                    //   color: effectiveTextColor.withOpacity(0.7),
                    //   fontSize: widget.fontSize,
                    // ),
                    style: FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontWeight: FontManager.medium,
                      color: effectiveTextColor.withOpacity(0.7),
                      fontSize: _getResponsiveValueFontSize(context),
                    ),
                  ),
                  const SizedBox(width: 8),
                ],
                Expanded(
                  child: Text(
                    _formattedDateRange,
                    // style: textStyle.copyWith(
                    //   color: effectiveTextColor.withOpacity(0.6),
                    //   fontSize: responsiveTitleFontSize,
                    // ),
                    style: FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontWeight: FontManager.medium,
                      color: effectiveTextColor.withOpacity(0.6),
                      fontSize: _getResponsiveValueFontSize(context),
                    ),
                    textAlign: TextAlign.left,
                    maxLines: 1, // ✅ no wrapping
                    overflow: TextOverflow.ellipsis, // ✅ show ...
                    softWrap: false,
                  ),
                ),
                if (widget.suffix != null) ...[
                  const SizedBox(width: 8),
                  Text(
                    widget.suffix!,
                    // style: TextStyle(
                    //   color: effectiveTextColor.withOpacity(0.7),
                    //   fontSize: widget.fontSize,
                    // ),
                    style: FontManager.getCustomStyle(
                      fontFamily: FontManager.fontFamilyInter,
                      fontWeight: FontManager.medium,
                      color: effectiveTextColor.withOpacity(0.7),
                      fontSize: _getResponsiveValueFontSize(context),
                    ),
                  ),
                ],
                if (widget.showCalendarIcon) ...[
                  //const SizedBox(width: 0),
                  SvgPicture.asset(
                    _isHovered
                        ? 'assets/images/date-hover.svg'
                        : 'assets/images/date.svg',
                    package: 'ui_controls_library',
                    //width: responsiveIconSize,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );

    // Build the final widget with label to match the image
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [content],
    );
  }
}

EdgeInsets _getResponsivePadding(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth >= 1440) {
    return const EdgeInsets.symmetric(
      horizontal: 16.0,
      vertical: 4.0,
    ); // Extra Large
  } else if (screenWidth >= 1280) {
    return const EdgeInsets.symmetric(
      horizontal: 12.0,
      vertical: 3.0,
    ); // Large// Large
  } else if (screenWidth >= 768) {
    return const EdgeInsets.symmetric(
      horizontal: 8.0,
      vertical: 2.0,
    ); // Medium// Medium
  } else {
    return const EdgeInsets.symmetric(
      horizontal: 6.0,
      vertical: 1.0,
    ); // Default for very small screens
  }
}

double _getResponsiveHeight(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 56.0; // Extra Large (>1920px)
  } else if (screenWidth >= 1440) {
    return 48.0; // Large (1440-1920px)
  } else if (screenWidth >= 1280) {
    return 40.0; // Medium (1280-1366px)
  } else if (screenWidth >= 768) {
    return 40.0; // Small (768-1024px)
  } else {
    return 40.0; // Default for very small screens
  }
}

double _getResponsiveValueFontSize(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;

  if (screenWidth > 1920) {
    return 18.0; // Extra Large
  } else if (screenWidth >= 1440) {
    return 16.0; // Large
  } else if (screenWidth >= 1280) {
    return 14.0; // Medium
  } else {
    return 14.0; // Default for very small screens
  }
}
